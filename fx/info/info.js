import { FxElement, html, css } from '../../fx.js';
import '../button/button.js';

customElements.define('fx-info', class FxInfo extends FxElement {
    static properties = {
        id: { type: String, default: '' },
        _id: { type: String, default: '' },
        base: { type: Object }
    }
    get isVisible() {
        let base = this.base || FX.base || {};
        if (base?.fxSelected?.is?.toLowerCase().includes('total') && !this.id) return false;
        return true;
     }

    async linkClick(e) {
        e.stopPropagation();
        const base = this.base || FX.base;
        if (!base || !base.fxSelected) return;
        if (!this.id) {
            this.id ||= 'info:' + base.fxSelected._id + ':' + this._id || ('table-row:' + FX.ulid());
            this.fire('changeInfo', this.id);
        }
        let _id = this.id;
        await base.getInfo(_id, base.dbLocal, 'addNotebook');
        this.async(() => {
            const tabIndex = base.addNotebooks.length - 1;
            const tabId = `add-info-${tabIndex}`;
            base.main.fxTabs.selectTab('', tabId, true);
        }, 100)
        this.$update();
    }

    static styles = css`

    `

    render() {
        if (!this.isVisible) return html``;
        return html`
            <fx-icon class="pointer" name="emojione-v1:circled-information-source" @click=${this.linkClick} style="opacity: ${this.id ? .6 : .1}"></fx-icon>
        `
    }
})
