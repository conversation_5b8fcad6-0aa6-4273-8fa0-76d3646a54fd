import { FxElement, html, css } from '../../../fx.js';

let $00;

customElements.define('fx-family-tree', class FxFamilyTree extends FxElement {
    static properties = {
        allPersons: { type: Object },
        item: { type: Object, notify: true },
        defScale: { type: Object, default: { x: .8, y: .8 } },
        scale: { type: Object, default: { x: .8, y: .8 } },
        isReady: { type: Boolean, default: false },
        base: { type: Object }
    }

    firstUpdated() {
        super.firstUpdated();
        this.$listen('tab-selected', (e) => {
            this.activeTabName = e?.get?.("value").activeTabName;
            if (this.activeTabName === 'family tree')
                this.init();
        })
        setTimeout(() => {
            this.init();
        }, 300)
    }

    'item-changed'(e) {
        this._init = false;
        this.init();
    }
    async init() {
        let selected = this.item || this.base?.item;
        this.allPersons = this.base?.fxFlat;
        if (!selected || !this.allPersons || this._init) return;
        if (this.activeTabName === 'family tree' || this.base.main.activeTabName === 'family tree')
            this._init = true;
        else
            return;
        if (this.$qs('iframe')) {
            this.$qs('iframe').srcdoc = '';
            this.$update();
        }

        let data = [];

        const allPersons = { ...this.allPersons };
        const doc = selected;
        if (doc.is === 'man' || doc.is === 'woman' || doc.spouses?.length) {
            let d1 = doc.dateStart?.split('T')?.[0];
            d1 = d1 ? d1.split('-').reverse().join('.') : '';
            let d2 = doc.dateEnd?.split('T')?.[0];
            d2 = d2 ? d2.split('-').reverse().join('.') : '';
            const desc = d1 + ' ... ' + d2;
            const children = [];
            Object.values(allPersons).map(i => {
                if (i.is === 'man' || i.is === 'woman') {
                    if (i.father == doc._id || i.mother == doc._id) {
                        children.push(i._id);
                    }
                }
            })
            const person = {
                id: doc._id,
                data: {
                    label: doc.label,
                    gender: doc.is === 'man' ? 'M' : 'F',
                    main: true,
                    desc,
                    avatar: doc.photo || (doc.is === 'man' ? '/fx/persona/man.jpg' : '/fx/persona/woman.jpg')

                },
                rels: {
                    father: doc.father || '',
                    mother: doc.mother || '',
                    spouses: Object.keys(doc.spouses || []).map(i => i),
                    children
                },
            }
            children.map(i => {
                let spouse;
                if (person.data.gender === 'M') {
                    spouse = allPersons[i].mother;
                } else {
                    spouse = allPersons[i].father;
                }
                person.rels.spouses.add(spouse);
            })
            data.push(person);
        }
        if (data.length) {
            Object.keys(allPersons).map(async key => {
                const doc = allPersons[key];
                if (doc.is === 'man' || doc.is === 'woman' || doc.spouses?.length) {
                    let d1 = doc.dateStart?.split('T')?.[0];
                    d1 = d1 ? d1.split('-').reverse().join('.') : '';
                    let d2 = doc.dateEnd?.split('T')?.[0];
                    d2 = d2 ? d2.split('-').reverse().join('.') : '';
                    const desc = d1 + ' ... ' + d2;
                    const children = [];
                    Object.keys(allPersons).map(k => {
                        if (allPersons[k]?.father === doc._id || allPersons[k]?.mother === doc._id) {
                            children.push(k);
                        }
                    })
                    const person = {
                        id: doc._id,
                        data: {
                            label: doc.label,
                            gender: doc.is === 'man' ? 'M' : 'F',
                            desc,
                            avatar: doc.photo || (doc.is === 'man' ? '/fx/persona/man.jpg' : '/fx/persona/woman.jpg')
                        },
                        rels: {
                            father: doc.father || '',
                            mother: doc.mother || '',
                            spouses: Object.keys(doc.spouses || []).map(i => i),
                            children
                        },
                    }
                    children.map(i => {
                        let spouse;
                        if (person.data.gender === 'M') {
                            spouse = allPersons[i].mother;
                        } else {
                            spouse = allPersons[i].father;
                        }
                        person.rels.spouses.add(spouse);
                    })
                    data.push(person);
                }
            })
            data = JSON.stringify(data);
            this.isReady = true;
            setTimeout(() => {
                this.$qs('iframe').srcdoc = this.src(data);
                this.$update();
            })
        }
    }

    static styles = css`
            :host::-webkit-scrollbar { width: 4px; height: 4px; }
            :host {
                display: flex;
                flex-direction: column;
                flex: 1;
                /* color: red; */
                width: 100%;
                height: 100%;
                /* background: rgb(59, 85, 96); */
             }
        `

    render() {
        return html`
            ${this.isReady ? html`
                <iframe style="width: 100%; height: 100%; border: none; "></iframe>
            ` : html``}
        `
    }

    src(data) {
        return `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <title>basic-tree-1</title>
    <link rel="shortcut icon" type="image/svg" href="/examples/logo.svg">
    <script src="/fx/~/family-chart/plugins/d3.v6.js"></script>
    <link rel="stylesheet" href="/fx/~/family-chart/styles/main.css">
    <style>
        html, body {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            display: flex;
            flex: 1;
            position: relative;
            overflow: hidden;
        }
    </style>
</head>

<body>
    <div id="FamilyChart"
        style="height: 100vh; width: 100%; max-width:100%; margin: auto; position: relative">
    </div>
    <script type="module">
        import f3 from '/fx/~/family-chart/index.js'
        let data = ${data};
        const store = f3.createStore({
            data,
            node_separation: 400,
            level_separation: 150
        }),
            view = f3.d3AnimationView({
                store,
                cont: document.querySelector("#FamilyChart")
            }),
            Card = f3.elements.Card({
                store,
                svg: view.svg,
                card_dim: { w: 360, h: 100, text_x: 80, text_y: 15, img_w: 60, img_h: 90, img_x: 5, img_y: 5 },
                card_display: [d => d.data.label || '', d => d.data.desc || '',  d => d.data.info || ''],
                mini_tree: true,
                link_break: false
            })

        view.setCard(Card)
        store.setOnUpdate(props => view.update(props || {}))
        store.update.tree({ initial: true })
    </script>
</body>

</html>
`
    }
})
