import { css } from '/fx.js';

export const $styles = css`
    :host {
        display: flex;
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 0;
    }
    .loader {
        position: absolute;
        height: 96px;
        width: 96px;
        top: 50%;
        left: 50%;
        margin-left: -48px;
        margin-top: -48px;
        border: 12px solid lightgray;
        border-radius: 50%;
        border-top: 12px solid violet;
        border-bottom: 12px solid violet;
        animation: spin 1s linear infinite;
        pointer-events: none;
        z-index: 9999;
        opacity: .7;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`

export const $btnsMT = [
    { id: 'files', name: 'codicon:files:42', scale: .6 },
    { id: 'search', name: 'codicon:search:42', scale: .65 },
    { id: 'persons', name: 'carbon:pedestrian-family:42', scale: .65 },
]

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

let moneyTotal = `
<svg viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--noto" preserveAspectRatio="xMidYMid meet">

<g fill="none">

<path d="M93.46 39.45c6.71-1.49 15.45-8.15 16.78-11.43c.78-1.92-3.11-4.92-4.15-6.13c-2.38-2.76-1.42-4.12-.5-7.41c1.05-3.74-1.44-7.87-4.97-9.49s-7.75-1.11-11.3.47c-3.55 1.58-6.58 4.12-9.55 6.62c-2.17-1.37-5.63-7.42-11.23-3.49c-3.87 2.71-4.22 8.61-3.72 13.32c1.17 10.87 3.85 16.51 8.9 18.03c6.38 1.92 13.44.91 19.74-.49z" fill="#FFCA28">

</path>

<path d="M104.36 8.18c-.85 14.65-15.14 24.37-21.92 28.65l4.4 3.78s2.79.06 6.61-1.16c6.55-2.08 16.12-7.96 16.78-11.43c.97-5.05-4.21-3.95-5.38-7.94c-.61-2.11 2.97-6.1-.49-11.9zm-24.58 3.91s-2.55-2.61-4.44-3.8c-.94 1.77-1.61 3.69-1.94 5.67c-.59 3.48 0 8.42 1.39 12.1c.22.57 1.04.48 1.13-.12c1.2-7.91 3.86-13.85 3.86-13.85z" fill="#E2A610">

</path>

<path d="M61.96 38.16S30.77 41.53 16.7 68.61c-14.07 27.08-2.11 43.5 10.55 49.48c12.66 5.98 44.56 8.09 65.31 3.17s25.94-15.12 24.97-24.97c-1.41-14.38-14.77-23.22-14.77-23.22s.53-17.76-13.25-29.29c-12.23-10.24-27.55-5.62-27.55-5.62z" fill="#FFCA28">

</path>

<path d="M74.76 83.73c-6.69-8.44-14.59-9.57-17.12-12.6c-1.38-1.65-2.19-3.32-1.88-5.39c.33-2.2 2.88-3.72 4.86-4.09c2.31-.44 7.82-.21 12.45 4.2c1.1 1.04.7 2.66.67 4.11c-.08 3.11 4.37 6.13 7.97 3.53c3.61-2.61.84-8.42-1.49-11.24c-1.76-2.13-8.14-6.82-16.07-7.56c-2.23-.21-11.2-1.54-16.38 8.31c-1.49 2.83-2.04 9.67 5.76 15.45c1.63 1.21 10.09 5.51 12.44 8.3c4.07 4.83 1.28 9.08-1.9 9.64c-8.67 1.52-13.58-3.17-14.49-5.74c-.65-1.83.03-3.81-.81-5.53c-.86-1.77-2.62-2.47-4.48-1.88c-6.1 1.94-4.16 8.61-1.46 12.28c2.89 3.93 6.44 6.3 10.43 7.6c14.89 4.85 22.05-2.81 23.3-8.42c.92-4.11.82-7.67-1.8-10.97z" fill="#6B4B46">

</path>

<path d="M71.16 48.99c-12.67 27.06-14.85 61.23-14.85 61.23" stroke="#6B4B46" stroke-width="5" stroke-miterlimit="10">

</path>

<path d="M81.67 31.96c8.44 2.75 10.31 10.38 9.7 12.46c-.73 2.44-10.08-7.06-23.98-6.49c-4.86.2-3.45-2.78-1.2-4.5c2.97-2.27 7.96-3.91 15.48-1.47z" fill="#6D4C41">

</path>

<path d="M81.67 31.96c8.44 2.75 10.31 10.38 9.7 12.46c-.73 2.44-10.08-7.06-23.98-6.49c-4.86.2-3.45-2.78-1.2-4.5c2.97-2.27 7.96-3.91 15.48-1.47z" fill="#6B4B46">

</path>

<path d="M96.49 58.86c1.06-.73 4.62.53 5.62 7.5c.49 3.41.64 6.71.64 6.71s-4.2-3.77-5.59-6.42c-1.75-3.35-2.43-6.59-.67-7.79z" fill="#E2A610">

</path>

</g>

</svg>
`
const weddingCouple = `
<svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 192.218 192.218" xml:space="preserve">
<g>
	<g>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" points="139.868,59.515 150.065,60.765 153.003,44.613 
			142.807,43.364 		"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#D9ECED;" d="M42.867,16.54c24.19-6.091,51.487,18.689,62.622,55.964
			C95.45,85.526,81.076,95.42,63.821,99.765c-17.254,4.345-34.6,2.437-49.607-4.279C6.369,57.383,18.676,22.631,42.867,16.54z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M75.618,72.693c4.62-1.206,14.358-5.043,17.962-8.06
			l2.035,3.481C92.585,72,82.841,77.31,78.458,79.754L75.618,72.693z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M61.695,68.729c5.153,1.444,11.526,2.63,18.295,3.255
			l-0.798,7.444c-6.294,0.353-11.99,0.216-15.778-0.386C52.816,77.36,50.42,65.568,61.695,68.729z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M91.03,63.271c0.658-1.295,1.477-1.362,2.172-0.606
			c0.722-2.026,2.817-2.923,3.824-2.411c1.959,0.995,2.451,3.96,1.099,6.621c-1.353,2.662-4.036,4.013-5.996,3.018
			C90.17,68.897,89.678,65.933,91.03,63.271z"/>
		
			<rect x="50.657" y="59.49" transform="matrix(-0.968 0.2509 -0.2509 -0.968 128.1275 119.0598)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" width="11.633" height="16.416"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M62.296,105.684l-9.273-19.877l-1.808-15.216
			c3.843-0.349,7.594-1.342,11.261-2.918c2.957,0.66,7.304,6.845,8.068,12.755l3.331,22.255L62.296,105.684z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M70.589,101.248c24.945,0,45.366,30.695,47.062,69.56
			c-12.914,10.177-29.269,16.262-47.062,16.262c-17.793,0-29.428-8.23-42.341-18.407C29.943,129.798,45.644,101.248,70.589,101.248z
			"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M53.023,85.807c1.392-0.335,9.265-11.864,15.74-11.179
			c0.869,1.82,1.524,3.825,1.78,5.8l3.298,22.029l0.033,0.226l-11.579,3.001L53.023,85.807z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M62.975,76.206c9.295,8.038,24.634,17.502,39.36,21.626
			l-1.217,4.013c-14.233-2.01-33.279-9.635-41.144-14.983C50.58,80.473,53.599,68.097,62.975,76.206z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M74.49,192.218c4.62,0,8.388-1.893,8.388-4.213
			c0-2.319-3.768-4.212-8.388-4.212c-4.619,0-8.387,1.893-8.387,4.212C66.104,190.325,69.871,192.218,74.49,192.218z"/>
		<g>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" d="M127.086,186.817l-2.566-55.118l-0.018-12.87l-0.013-4.289
				h14.801c-1.082,24.093-2.165,48.184-3.247,72.277C133.057,186.817,130.07,186.817,127.086,186.817z"/>
			<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" points="124.488,114.54 124.694,114.54 124.501,118.831 
				124.501,118.829 			"/>
		</g>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M126.845,192.218c5.065,0,9.197-2.075,9.197-4.619
			c0-2.544-4.132-4.619-9.197-4.619c-5.065,0-9.196,2.075-9.196,4.619C117.648,190.143,121.779,192.218,126.845,192.218z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M123.789,69.206c-5.521-0.494-16.764-4.198-21.469-6.96
			l-1.662,4.376c4.216,3.867,15.746,9.256,21.233,11.213L123.789,69.206z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M140.004,63.063c-5.759,2.332-12.958,4.524-20.687,6.12
			l1.882,8.484c7.307-0.406,13.86-1.3,18.152-2.482C151.361,71.875,152.603,57.961,140.004,63.063z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M102.724,60.413c-1.331-1.033-2.224-0.699-2.58,0.44
			c-1.762-1.771-4.405-1.674-5.21-0.639c-1.563,2.016-0.612,5.371,2.126,7.497c2.737,2.125,6.225,2.215,7.789,0.199
			C106.413,65.895,105.462,62.539,102.724,60.413z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M106.323,96.788c1.358,0.72,1.415,1.587,0.602,2.31
			c2.132,0.802,3.042,3.036,2.481,4.093c-1.09,2.055-4.236,2.522-7.028,1.041c-2.793-1.48-4.174-4.347-3.084-6.402
			C100.384,95.773,103.53,95.308,106.323,96.788z"/>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" points="140.652,53.995 151.24,55.51 150.439,61.107 
			138.877,63.977 		"/>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" points="122.327,112.566 146.868,116.076 146.48,121.085 
			121.635,117.531 		"/>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" points="146.611,119.419 150.971,57.662 142.723,58.286 
			126.697,87.02 121.859,115.763 		"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M141.539,55.326c-3.98,18.962-4.857,28.249-14.379,33.752
			l1.149-8.203l10.411-19.682l1.933-7.198L141.539,55.326z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" d="M141.619,52.967l-1.692,12.369l-0.836,1.848l-8.241,17.327
			l-5.694,9.088l2.931-13.361l10.634-19.044l1.501-6.259c0.229-0.952,0.123-4.76,0.123-4.76L141.619,52.967z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M139.443,64.877c-7.162,11.414-20.203,26.266-34.317,35.32
			l2.564,3.816c14.277-6.681,31.795-20.794,38.323-28.929C153.81,65.368,146.668,53.363,139.443,64.877z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M104.561,96.746c-1.381,0.967-1.325,1.918-0.343,2.595
			c-2.212,1.16-2.901,3.714-2.149,4.788c1.462,2.09,4.95,2.173,7.789,0.186c2.84-1.987,3.956-5.292,2.494-7.383
			C110.889,94.842,107.4,94.759,104.561,96.746z"/>
		<g>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" d="M154.549,185.254l-20.446-51.25l-4.226-12.156l-1.414-4.05
				l13.987-4.839c6.854,23.122,13.708,46.244,20.563,69.367C160.192,183.302,157.37,184.278,154.549,185.254z"/>
			<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" points="128.463,117.798 128.657,117.73 129.878,121.85 
				129.877,121.848 			"/>
		</g>
		
			<ellipse transform="matrix(-0.327 -0.945 0.945 -0.327 29.2796 392.9934)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" cx="154.577" cy="186.071" rx="4.619" ry="9.197"/>
		
			<ellipse transform="matrix(0.3481 -0.9375 0.9375 0.3481 -141.5662 163.8561)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" cx="47.03" cy="183.715" rx="4.213" ry="8.387"/>
		<g>
			
				<ellipse transform="matrix(0.4068 -0.9135 0.9135 0.4068 69.7285 183.6045)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="176.242" cy="38.11" rx="5.78" ry="3.699"/>
			
				<ellipse transform="matrix(-0.0363 -0.9993 0.9993 -0.0363 102.7389 149.9801)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="123.684" cy="25.454" rx="5.781" ry="3.699"/>
			
				<ellipse transform="matrix(0.1902 -0.9817 0.9817 0.1902 87.1974 173.969)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="149.051" cy="34.129" rx="20.796" ry="26.979"/>
			
				<ellipse transform="matrix(0.1902 -0.9817 0.9817 0.1902 84.4548 163.5784)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="141.381" cy="30.597" rx="2.624" ry="1.499"/>
			
				<ellipse transform="matrix(0.1902 -0.9817 0.9817 0.1902 94.4271 181.9119)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="157.484" cy="33.716" rx="2.624" ry="1.499"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M172.931,19.088c-13.024-3.085-38.182,13.643-50.558,5.591
				c5.658-15.662,24.02-21.512,37.684-16.426C165.101,10.129,169.54,12.703,172.931,19.088z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M164.05,11.641c-4.798,10.628,3.711,25.086,12.192,26.469
				C179.388,29.184,173.471,15.208,164.05,11.641z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" d="M156.321,43.411c-3.952,0-13.266-1.872-17.482-3.387
				C142.283,46.283,151.661,48.295,156.321,43.411z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M172.839,19.56c-17.68-7.8-47.153,10.951-50.322-11.739
				c8.51,5.233,10.184,2.012,19.772-0.934C151.961,3.915,165.3,5.361,172.839,19.56z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M174.265,24.602C159.27,12.413,125.893,22.723,128.823,0
				c6.827,7.293,9.292,4.628,19.318,4.316C158.254,4.002,170.739,8.917,174.265,24.602z"/>
		</g>
		<g>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 27.4434 86.9165)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" cx="46.841" cy="33.001" rx="15.363" ry="15.517"/>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 25.4351 91.9061)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" cx="47.738" cy="36.261" rx="11.981" ry="17.54"/>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 24.7809 93.5307)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" cx="48.03" cy="37.323" rx="10.88" ry="19.508"/>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 17.5442 110.9603)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="51.053" cy="48.795" rx="19.555" ry="22.062"/>
			<g>
				
					<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 29.2611 114.7157)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="58.34" cy="46.209" rx="2.467" ry="1.409"/>
				
					<ellipse transform="matrix(-0.2651 -0.9642 0.9642 -0.2651 6.4928 105.5464)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="43.469" cy="50.299" rx="2.467" ry="1.41"/>
			</g>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" d="M61.565,53.555c-3.531,1.748-12.677,4.195-17.112,4.707
				C50.297,62.329,59.563,59.978,61.565,53.555z"/>
			
				<ellipse transform="matrix(-0.476 -0.8794 0.8794 -0.476 -4.6912 106.6855)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="29.438" cy="54.74" rx="5.761" ry="3.687"/>
			
				<ellipse transform="matrix(-0.0408 -0.9992 0.9992 -0.0408 32.4031 117.4178)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="72.564" cy="43.155" rx="5.761" ry="3.687"/>
			<g>
				<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M50.418,27.82c11.975,3.284,20.086,17.828,25.788,14.232
					C72.165,29.776,58.548,23.654,50.418,27.82z"/>
				<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M62.125,27.505c-17.185,3.736-21.34,29.617-36.561,27.665
					C21.797,36.275,40.678,22.316,62.125,27.505z"/>
			</g>
		</g>
	</g>
</g>
</svg>
`
const parents = `
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 503.467 503.467" xml:space="preserve">
<g transform="translate(1 9)">
	<path style="fill:#FFD0A1;" d="M412.867,123.267V157.4c0,25.6-17.067,59.733-42.667,59.733S327.534,183,327.534,157.4v-34.133
		c0,0,25.6,0,34.133-25.6C370.201,123.267,412.867,123.267,412.867,123.267z M216.601,89.134V106.2c0,4.267-3.413,8.533-8.533,8.533
		c0,17.067-8.533,34.133-8.533,34.133c-8.533,17.067-17.92,34.133-51.2,34.133s-42.667-17.067-51.2-34.133
		c0,0-8.533-17.067-8.533-34.133c-4.267,0-8.533-4.267-8.533-8.533V89.134c0-4.267,4.267-8.533,8.533-8.533v-8.533
		c0-23.893,18.773-42.667,42.667-42.667h34.133c23.893,0,42.667,18.773,42.667,42.667V80.6
		C213.187,80.6,216.601,84.867,216.601,89.134z"/>
	<path style="fill:#C49AE9;" d="M370.201,285.4c42.667,0,42.667-51.2,42.667-51.2c17.067,0,30.72,2.56,42.667,7.68l0,0
		c29.867,11.947,42.667,36.693,42.667,60.587V439l-51.2,8.533v8.533h-153.6V439V268.334c0-9.387-3.413-17.92-9.387-24.747
		l0.853-0.853c20.48-8.533,41.813-8.533,42.667-8.533C327.534,234.2,327.534,285.4,370.201,285.4"/>
	<path style="fill:#AE938D;" d="M412.867,157.4v-34.133c0,0-42.667,0-51.2-25.6c-8.533,25.6-34.133,25.6-34.133,25.6V157.4
		c0,25.6,17.067,59.733,42.667,59.733S412.867,183,412.867,157.4 M438.467,97.667l17.067,144.213
		c-11.947-4.267-25.6-7.68-42.667-7.68c0,0,0,51.2-42.667,51.2s-42.667-51.2-42.667-51.2c-0.853,0-22.187,0-42.667,8.533
		l17.067-145.067c2.56-25.6,34.133-51.2,59.733-51.2h17.067C404.334,46.467,436.761,72.067,438.467,97.667"/>
	<polyline style="fill:#AAB1BA;" points="187.587,225.667 189.294,225.667 148.334,421.934 107.374,225.667 109.08,225.667 	"/>
	<path style="fill:#80D6FA;" d="M148.334,234.2c5.12,0,8.533-3.413,8.533-8.533c0-5.12-3.413-8.533-8.533-8.533
		s-8.533,3.413-8.533,8.533C139.801,230.787,143.214,234.2,148.334,234.2z M54.467,447.534L3.267,439V268.334
		c0-34.133,42.667-51.2,102.4-51.2l1.707,8.533l40.96,196.267l40.96-196.267l1.707-8.533c42.667,0,76.8,8.533,93.013,26.453
		c5.973,6.827,9.387,15.36,9.387,24.747V439l-51.2,8.533v8.533H54.467V447.534z"/>
</g>
<path style="fill:#51565F;" d="M448.001,469.334L448.001,469.334c-2.56,0-4.267-1.707-4.267-4.267l0,0l0.853-136.533
	c0-2.56,1.707-4.267,4.267-4.267l0,0c2.56,0,4.267,1.707,4.267,4.267l0,0l-0.853,136.533
	C452.267,467.627,450.561,469.334,448.001,469.334z M243.201,469.334c-2.56,0-4.267-1.707-4.267-4.267V328.534
	c0-2.56,1.707-4.267,4.267-4.267s4.267,1.707,4.267,4.267v136.533C247.467,467.627,245.761,469.334,243.201,469.334z
	 M55.467,469.334c-2.56,0-4.267-1.707-4.267-4.267V328.534c0-2.56,1.707-4.267,4.267-4.267c2.56,0,4.267,1.707,4.267,4.267v136.533
	C59.734,467.627,58.027,469.334,55.467,469.334z M499.201,452.267c-2.56,0-4.267-1.707-4.267-4.267V311.467
	c0-16.213-5.973-30.72-17.067-41.813C464.214,256,443.734,248.32,418.134,248.32c-1.707,18.773-11.093,48.64-42.667,51.2v46.933
	c0,2.56-1.707,4.267-4.267,4.267s-4.267-1.707-4.267-4.267V299.52c-33.28-2.56-42.667-36.693-42.667-55.467
	c0-2.56,1.707-4.267,4.267-4.267l0,0c2.56,0,4.267,1.707,4.267,4.267c0,1.707,0.853,46.933,38.4,46.933s38.4-45.227,38.4-46.933
	c0-2.56,1.707-4.267,4.267-4.267c29.867,0,53.76,8.533,69.973,23.893c12.8,12.8,19.627,29.867,19.627,47.787V448
	C503.467,450.56,501.761,452.267,499.201,452.267z M294.401,452.267c-2.56,0-4.267-1.707-4.267-4.267V277.334
	c0-9.387-3.413-17.067-10.24-23.893c-14.507-14.507-44.373-23.04-84.48-23.04l-41.813,201.387v0.853
	c0,0.853-0.853,0.853-0.853,1.707c-0.853,0.853-0.853,0.853-1.707,0.853c-0.853,0-0.853,0-1.707,0l0,0l0,0c-0.853,0-0.853,0-1.707,0
	s-0.853-0.853-1.707-0.853c-0.853,0-0.853-0.853-0.853-1.707v-0.853L103.253,230.4c-40.107,0-69.973,8.533-84.48,23.04
	c-6.827,6.827-10.24,14.507-10.24,23.893V448c0,2.56-1.707,4.267-4.267,4.267S0,450.56,0,448V277.334
	C0,266.24,4.267,256,12.8,247.467c17.067-17.067,49.493-25.6,93.867-25.6c2.56,0,4.267,0.853,5.12,3.413l38.4,184.32l38.4-184.32
	c0.853-2.56,2.56-3.413,5.12-3.413c43.52,0,76.8,9.387,93.867,25.6c8.533,8.533,12.8,17.92,12.8,29.867V448
	C298.667,450.56,296.961,452.267,294.401,452.267z M149.334,315.734c-2.56,0-4.267-1.707-4.267-4.267v-64.853
	c-5.12-1.707-8.533-6.827-8.533-11.947c0-6.827,5.973-12.8,12.8-12.8s12.8,5.973,12.8,12.8c0,5.973-3.413,10.24-8.533,11.947v64.853
	C153.601,314.027,151.894,315.734,149.334,315.734z M149.334,230.4c-2.56,0-4.267,1.707-4.267,4.267c0,2.56,1.707,4.267,4.267,4.267
	s4.267-1.707,4.267-4.267C153.601,232.107,151.894,230.4,149.334,230.4z M371.201,230.4c-29.013,0-46.933-37.547-46.933-64v-34.133
	c0-2.56,1.707-4.267,4.267-4.267c0.853,0,23.04,0,29.867-23.04c0.853-3.413,6.827-3.413,7.68,0
	c7.68,22.187,46.933,23.04,46.933,23.04c2.56,0,4.267,1.707,4.267,4.267V166.4C418.134,192.854,400.214,230.4,371.201,230.4z
	 M332.801,136.534V166.4c0,23.04,15.36,55.467,38.4,55.467s38.4-32.427,38.4-55.467v-29.867
	c-10.24-0.853-34.987-4.267-46.933-19.627C354.987,129.707,342.187,134.827,332.801,136.534z M448.001,221.867
	c-2.56,0-4.267-1.707-4.267-4.267l-8.533-110.933c-1.707-22.187-31.573-46.933-55.467-46.933h-17.067
	c-24.747,0-53.76,24.747-55.467,46.933L298.667,217.6c0,2.56-2.56,4.267-4.267,4.267c-2.56,0-4.267-2.56-4.267-4.267l8.533-110.933
	c2.56-26.453,34.987-55.467,64-55.467h17.067c26.453,0,61.44,25.6,64,55.467l8.533,110.933
	C452.267,219.307,450.561,221.867,448.001,221.867L448.001,221.867z M149.334,196.267c-36.693,0-46.933-21.333-54.613-36.693
	c0-0.853-7.68-16.213-8.533-32.427c-5.12-1.707-8.533-6.827-8.533-11.947V98.134c0-5.973,3.413-10.24,8.533-11.947v-5.12
	c0-25.6,21.333-46.933,46.933-46.933h34.133c25.6,0,46.933,21.333,46.933,46.933v5.12c5.12,1.707,8.533,6.827,8.533,11.947V115.2
	c0,5.973-3.413,10.24-8.533,11.947c-0.853,16.213-8.533,31.573-8.533,32.427C196.267,174.934,186.027,196.267,149.334,196.267z
	 M132.267,42.667c-21.333,0-38.4,17.067-38.4,38.4V89.6c0,2.56-1.707,4.267-4.267,4.267s-4.267,1.707-4.267,4.267V115.2
	c0,2.56,1.707,4.267,4.267,4.267s4.267,1.707,4.267,4.267c0,16.213,7.68,32.427,7.68,32.427
	c9.387,18.773,17.92,31.573,47.787,31.573s38.4-12.8,47.787-31.573c0,0,7.68-16.213,7.68-32.427c0-2.56,1.707-4.267,4.267-4.267
	s4.267-1.707,4.267-4.267V98.134c0-2.56-1.707-4.267-4.267-4.267s-4.267-1.707-4.267-4.267v-8.533c0-21.333-17.067-38.4-38.4-38.4
	H132.267z M174.934,110.934h-8.533c-2.56,0-4.267-1.707-4.267-4.267c0-2.56,1.707-4.267,4.267-4.267h8.533
	c2.56,0,4.267,1.707,4.267,4.267C179.201,109.227,177.494,110.934,174.934,110.934z M132.267,110.934h-8.533
	c-2.56,0-4.267-1.707-4.267-4.267c0-2.56,1.707-4.267,4.267-4.267h8.533c2.56,0,4.267,1.707,4.267,4.267
	C136.534,109.227,134.827,110.934,132.267,110.934z"/>
</svg>
`

const usedIcons =
{
    "weddingCouple": weddingCouple,
    "parents": parents,
    "moneyTotal": moneyTotal,
    "fc-currency_exchange": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <circle fill=\"#3F51B5\" cx=\"18\" cy=\"18\" r=\"15\"/>\r\n    <path fill=\"#FFF59D\" d=\"M20.3,16v1.7h-3.8v1.4h3.8v1.7h-3.8c0,0.6,0.1,1.2,0.3,1.6c0.2,0.4,0.4,0.8,0.7,1c0.3,0.3,0.7,0.4,1.1,0.6 c0.4,0.1,0.9,0.2,1.4,0.2c0.4,0,0.7,0,1.1-0.1c0.4-0.1,0.7-0.1,1-0.3l0.4,2.7c-0.4,0.1-0.9,0.2-1.4,0.2c-0.5,0.1-1,0.1-1.5,0.1 c-0.9,0-1.8-0.1-2.6-0.4c-0.8-0.2-1.5-0.6-2-1.1c-0.6-0.5-1-1.1-1.4-1.9c-0.3-0.7-0.5-1.6-0.5-2.6h-1.9v-1.7h1.9v-1.4h-1.9V16h1.9 c0.1-1,0.3-1.8,0.6-2.6c0.4-0.7,0.8-1.4,1.4-1.9c0.6-0.5,1.3-0.9,2.1-1.1c0.8-0.3,1.7-0.4,2.6-0.4c0.4,0,0.9,0,1.3,0.1 c0.4,0.1,0.9,0.1,1.3,0.3l-0.4,2.7c-0.3-0.1-0.6-0.2-1-0.3c-0.4-0.1-0.7-0.1-1.1-0.1c-0.5,0-1,0.1-1.4,0.2c-0.4,0.1-0.8,0.3-1,0.6 c-0.3,0.3-0.5,0.6-0.7,1s-0.3,0.9-0.3,1.5H20.3z\"/>\r\n    <circle fill=\"#4CAF50\" cx=\"30\" cy=\"30\" r=\"15\"/>\r\n    <path fill=\"#fff\" d=\"M28.4,27c0.1,0.2,0.2,0.4,0.4,0.6c0.2,0.2,0.4,0.4,0.7,0.5c0.3,0.2,0.7,0.3,1.1,0.5c0.7,0.3,1.4,0.6,2,0.9 c0.6,0.3,1.1,0.7,1.5,1.1c0.4,0.4,0.8,0.9,1,1.4c0.2,0.5,0.4,1.2,0.4,1.9c0,0.7-0.1,1.3-0.3,1.8c-0.2,0.5-0.5,1-0.9,1.4 s-0.9,0.7-1.4,0.9c-0.6,0.2-1.2,0.4-1.8,0.5v2.2h-1.8v-2.2c-0.6-0.1-1.2-0.2-1.8-0.4s-1.1-0.5-1.5-1c-0.5-0.4-0.8-1-1.1-1.6 c-0.3-0.6-0.4-1.4-0.4-2.3h3.3c0,0.5,0.1,1,0.2,1.3c0.1,0.4,0.3,0.6,0.6,0.9c0.2,0.2,0.5,0.4,0.8,0.5c0.3,0.1,0.6,0.1,0.9,0.1 c0.4,0,0.7,0,0.9-0.1c0.3-0.1,0.5-0.2,0.7-0.4c0.2-0.2,0.3-0.4,0.4-0.6c0.1-0.2,0.1-0.5,0.1-0.8c0-0.3,0-0.6-0.1-0.8 c-0.1-0.2-0.2-0.5-0.4-0.7s-0.4-0.4-0.7-0.5c-0.3-0.2-0.7-0.3-1.1-0.5c-0.7-0.3-1.4-0.6-2-0.9c-0.6-0.3-1.1-0.7-1.5-1.1 c-0.4-0.4-0.8-0.9-1-1.4c-0.2-0.5-0.4-1.2-0.4-1.9c0-0.6,0.1-1.2,0.3-1.7c0.2-0.5,0.5-1,0.9-1.4c0.4-0.4,0.9-0.7,1.4-1 c0.5-0.2,1.2-0.4,1.8-0.5v-2.4h1.8v2.4c0.6,0.1,1.2,0.3,1.8,0.6c0.5,0.3,1,0.6,1.3,1.1c0.4,0.4,0.7,1,0.9,1.6c0.2,0.6,0.3,1.3,0.3,2 h-3.3c0-0.9-0.2-1.6-0.6-2c-0.4-0.4-0.9-0.7-1.5-0.7c-0.3,0-0.6,0.1-0.9,0.2c-0.2,0.1-0.4,0.2-0.6,0.4c-0.2,0.2-0.3,0.4-0.3,0.6 c-0.1,0.2-0.1,0.5-0.1,0.8C28.3,26.5,28.4,26.8,28.4,27z\"/>\r\n</svg>\r\n",

    "fc-combo_chart": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <g fill=\"#00BCD4\">\r\n        <rect x=\"37\" y=\"18\" width=\"6\" height=\"24\"/>\r\n        <rect x=\"29\" y=\"26\" width=\"6\" height=\"16\"/>\r\n        <rect x=\"21\" y=\"22\" width=\"6\" height=\"20\"/>\r\n        <rect x=\"13\" y=\"32\" width=\"6\" height=\"10\"/>\r\n        <rect x=\"5\" y=\"28\" width=\"6\" height=\"14\"/>\r\n    </g>\r\n    <g fill=\"#3F51B5\">\r\n        <circle cx=\"8\" cy=\"16\" r=\"3\"/>\r\n        <circle cx=\"16\" cy=\"18\" r=\"3\"/>\r\n        <circle cx=\"24\" cy=\"11\" r=\"3\"/>\r\n        <circle cx=\"32\" cy=\"13\" r=\"3\"/>\r\n        <circle cx=\"40\" cy=\"9\" r=\"3\"/>\r\n        <polygon points=\"39.1,7.2 31.8,10.9 23.5,8.8 15.5,15.8 8.5,14.1 7.5,17.9 16.5,20.2 24.5,13.2 32.2,15.1 40.9,10.8\"/>\r\n    </g>\r\n</svg>\r\n",
    "fc-sports_mode": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <circle fill=\"#FF9800\" cx=\"28\" cy=\"9\" r=\"5\"/>\r\n    <path fill=\"#00796B\" d=\"M29,27.3l-9.2-4.1c-1-0.5-1.5,1-2,2c-0.5,1-4.1,7.2-3.8,8.3c0.3,0.9,1.1,1.4,1.9,1.4c0.2,0,0.4,0,0.6-0.1 L28.8,31c0.8-0.2,1.4-1,1.4-1.8C30.2,28.4,29.7,27.6,29,27.3z\"/>\r\n    <path fill=\"#009688\" d=\"M26.8,15.2l-2.2-1c-1.3-0.6-2.9,0-3.5,1.3L9.2,41.1c-0.5,1,0,2.2,1,2.7c0.3,0.1,0.6,0.2,0.9,0.2 c0.8,0,1.5-0.4,1.8-1.1c0,0,9.6-13.3,10.4-14.9s4.9-9.3,4.9-9.3C28.7,17.4,28.2,15.8,26.8,15.2z\"/>\r\n    <path fill=\"#FF9800\" d=\"M40.5,15.7c-0.7-0.8-2-1-2.8-0.3l-5,4.2l-6.4-3.5c-1.1-0.6-2.6-0.4-3.3,0.9c-0.8,1.3-0.4,2.9,0.8,3.4 l8.3,3.4c0.3,0.1,0.6,0.2,0.9,0.2c0.5,0,0.9-0.2,1.3-0.5l6-5C41.1,17.8,41.2,16.6,40.5,15.7z\"/>\r\n    <path fill=\"#FF9800\" d=\"M11.7,23.1l3.4-5.1l4.6,0.6l1.5-3.1c0.4-0.9,1.2-1.4,2.1-1.5c-0.1,0-0.2,0-0.2,0h-9c-0.7,0-1.3,0.3-1.7,0.9 l-4,6c-0.6,0.9-0.4,2.2,0.6,2.8C9.2,23.9,9.6,24,10,24C10.6,24,11.3,23.7,11.7,23.1z\"/>\r\n</svg>\r\n",
    "noto-v1:money-bag": {
        "svg": "<path fill=\"#fcc21b\" d=\"M68.43 5.38c.89-1.95 2.55-2.5 3.93-2.56c2.1-.1 4.3 1.35 6.37 3.06C80.2 7.54 81.96 8.82 84 9.72c1.41.5 2.81.6 4.23.53c2-.1 4.03-.54 6.04-.64l.11-.01l.91-.08c2.3-.11 4.47.43 6.53 1.62c1.89 1.72 2.72 3.86 2.46 6.44c-.44 2.13-.06 4.08 1.13 5.85c2.05 1.44 4.2 2.7 6.46 3.77c2.34 1.28 3.81 3.21 4.45 5.78c.23 2.72-1.03 4.46-3.75 5.21c-3.54.89-8.3.89-12.31 3.99c-5.86 4.89-7.7 9.82-10.25 10.57c-1.25-1.42-9.05-7.45-22.13-10.34c-2.34-8.07-3.76-27.55.55-37.03\"/><path fill=\"#d19b15\" d=\"M81.63 40.69c2.3-4.6 8.85-12.69 10.42-11.09c1.55 1.74-2.02 10.78-4.53 15.21c-.89-.94-3.76-2.91-5.01-3.24\"/><path fill=\"#8d6e63\" d=\"M93.2 55.42c-1.35 0-2.67-.69-3.42-1.93c-6.88-11.32-22.46-10.13-22.61-10.11c-2.2.18-4.14-1.44-4.33-3.63a4.006 4.006 0 0 1 3.61-4.34c.84-.08 20.67-1.71 30.17 13.94c1.15 1.89.54 4.35-1.34 5.49c-.66.39-1.37.58-2.08.58\"/><path fill=\"#fcc21b\" d=\"M96.06 122.07c-17.13 4.22-40.94 3.92-57.95.56C22.19 119.48 10 110.09 11.4 91.61c1.42-18.7 13.81-36.31 30.85-44.07c16.49-7.51 38.62-11.17 50.28 5.87c2.9 4.24 8.44 13.65 8.7 18.27c.02.37.04.7.12 1.02c.02 1.16.49 2.33 1.63 3.19c7.33 5.55 12.67 12.91 13.65 23c1.16 12.03-10.22 20.63-20.57 23.18\"/><path fill=\"#424242\" d=\"M80.87 91.13c-2.08-3.38-5.91-7.05-9.53-10.2l2.81-7.78c2.5 1.95 4.7 3.78 6.42 5.36c.28.27.72.29 1.04.06l6.44-4.79c.18-.14.29-.34.32-.57a.8.8 0 0 0-.21-.62c-1.91-2.13-6.05-5.86-10.58-8.93l1.65-4.56c.19-.52-.07-1.09-.6-1.29l-7.57-2.74c-.52-.19-1.1.08-1.29.6l-1.23 3.41c-7.13-2.21-12.79-1.5-17.23 2.21c-3.48 2.92-4.98 7.09-3.92 10.87c1.08 4.03 4.24 6.79 8.24 10.28l.51.45c.93.8 1.92 1.63 2.89 2.46l-3.42 9.46c-4.49-2.94-8.25-7.01-8.89-7.9a.806.806 0 0 0-1.08-.21l-7.42 4.74c-.18.11-.31.3-.36.52c-.04.21 0 .43.12.61c2.23 3.36 5.88 7.12 9.77 10.06c.93.7 2.33 1.68 4.05 2.71l-1.29 3.55c-.18.52.08 1.09.6 1.28l7.58 2.75c.52.19 1.1-.08 1.29-.6l1.07-2.96c6.92 1.88 13 .79 17.7-3.23c4.54-3.89 5.32-9.35 2.12-15M65.18 68.4l-2.07 5.73c-1.93-1.5-2.96-2.63-2.23-4.25c.76-1.66 2.65-1.77 4.3-1.48m2.05 23.9c1.33 1.29 2.27 2.48 2.36 3.57c.04.62-.19 1.22-.73 1.84c-.94 1.09-2.44 1.3-3.99 1.11z\"/>",
        "vb": "0 0 128 128",
        "strw": "0"
    },
    "fc-survey": {
        "svg": "<path fill=\"#455A64\" d=\"M36 4H26c0 1.1-.9 2-2 2s-2-.9-2-2H12C9.8 4 8 5.8 8 8v32c0 2.2 1.8 4 4 4h24c2.2 0 4-1.8 4-4V8c0-2.2-1.8-4-4-4\"/><path fill=\"#fff\" d=\"M36 41H12c-.6 0-1-.4-1-1V8c0-.6.4-1 1-1h24c.6 0 1 .4 1 1v32c0 .6-.4 1-1 1\"/><g fill=\"#90A4AE\"><path d=\"M26 4c0 1.1-.9 2-2 2s-2-.9-2-2h-7v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4z\"/><path d=\"M24 0c-2.2 0-4 1.8-4 4s1.8 4 4 4s4-1.8 4-4s-1.8-4-4-4m0 6c-1.1 0-2-.9-2-2s.9-2 2-2s2 .9 2 2s-.9 2-2 2\"/></g><path fill=\"#CFD8DC\" d=\"M21 20h12v2H21zm-6-1h4v4h-4z\"/><path fill=\"#03A9F4\" d=\"M21 29h12v2H21zm-6-1h4v4h-4z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc-currency_exchange": {
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc-database": {
        "svg": "<path fill=\"#D1C4E9\" d=\"M38 7H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2V9c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc-filing_cabinet": {
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc-reading": {
        "svg": "<path fill=\"#5C6BC0\" d=\"M40 40c-6.9 0-16 4-16 4V22s9-4 18-4z\"/><path fill=\"#7986CB\" d=\"M8 40c6.9 0 16 4 16 4V22s-9-4-18-4z\"/><g fill=\"#FFB74D\"><circle cx=\"24\" cy=\"12\" r=\"8\"/><path d=\"M41 32h1c.6 0 1-.4 1-1v-4c0-.6-.4-1-1-1h-1c-1.7 0-3 1.3-3 3s1.3 3 3 3M7 26H6c-.6 0-1 .4-1 1v4c0 .6.4 1 1 1h1c1.7 0 3-1.3 3-3s-1.3-3-3-3\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fx:settings": {
        "svg": "<path fill=\"currentColor\" d=\"M27 16.76v-1.53l1.92-1.68A2 2 0 0 0 29.3 11l-2.36-4a2 2 0 0 0-1.73-1a2 2 0 0 0-.64.1l-2.43.82a11 11 0 0 0-1.31-.75l-.51-2.52a2 2 0 0 0-2-1.61h-4.68a2 2 0 0 0-2 1.61l-.51 2.52a11.5 11.5 0 0 0-1.32.75l-2.38-.86A2 2 0 0 0 6.79 6a2 2 0 0 0-1.73 1L2.7 11a2 2 0 0 0 .41 2.51L5 15.24v1.53l-1.89 1.68A2 2 0 0 0 2.7 21l2.36 4a2 2 0 0 0 1.73 1a2 2 0 0 0 .64-.1l2.43-.82a11 11 0 0 0 1.31.75l.51 2.52a2 2 0 0 0 2 1.61h4.72a2 2 0 0 0 2-1.61l.51-2.52a11.5 11.5 0 0 0 1.32-.75l2.42.82a2 2 0 0 0 .64.1a2 2 0 0 0 1.73-1l2.28-4a2 2 0 0 0-.41-2.51ZM25.21 24l-3.43-1.16a8.9 8.9 0 0 1-2.71 1.57L18.36 28h-4.72l-.71-3.55a9.4 9.4 0 0 1-2.7-1.57L6.79 24l-2.36-4l2.72-2.4a8.9 8.9 0 0 1 0-3.13L4.43 12l2.36-4l3.43 1.16a8.9 8.9 0 0 1 2.71-1.57L13.64 4h4.72l.71 3.55a9.4 9.4 0 0 1 2.7 1.57L25.21 8l2.36 4l-2.72 2.4a8.9 8.9 0 0 1 0 3.13L27.57 20Z\"/><path fill=\"currentColor\" d=\"M16 22a6 6 0 1 1 6-6a5.94 5.94 0 0 1-6 6m0-10a3.91 3.91 0 0 0-4 4a3.91 3.91 0 0 0 4 4a3.91 3.91 0 0 0 4-4a3.91 3.91 0 0 0-4-4\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "codicon:layout-panel-off": {
        "svg": "<path fill=\"currentColor\" d=\"M2 1L1 2v12l1 1h12l1-1V2l-1-1zm0 9V2h12v8zm0 1h12v3H2z\"/>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "codicon:layout-sidebar-right-off": {
        "svg": "<path fill=\"currentColor\" d=\"M2 1L1 2v12l1 1h12l1-1V2l-1-1zm0 13V2h7v12zm8 0V2h4v12z\"/>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "codicon:close": {
        "svg": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m8 8.707l3.646 3.647l.708-.707L8.707 8l3.647-3.646l-.707-.708L8 7.293L4.354 3.646l-.707.708L7.293 8l-3.646 3.646l.707.708z\" clip-rule=\"evenodd\"/>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "carbon:pedestrian-family": {
        "svg": "<path fill=\"currentColor\" d=\"M20 30h-3a2 2 0 0 1-2-2v-5h2v5h3v-5h2v-4a1 1 0 0 0-1-1h-8.72l-2-6H4a1 1 0 0 0-1 1v6h2v9h4v-7h2v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a2 2 0 0 1-2-2v-6a3.003 3.003 0 0 1 3-3h6.28a2 2 0 0 1 1.897 1.367L13.72 16H21a3.003 3.003 0 0 1 3 3v4a2 2 0 0 1-2 2v3a2 2 0 0 1-2 2m8 0h-2V19h3v-6a1 1 0 0 0-1-1h-4v-2h4a3.003 3.003 0 0 1 3 3v6a2 2 0 0 1-2 2h-1zM7 9a4 4 0 1 1 4-4a4.005 4.005 0 0 1-4 4m0-6a2 2 0 1 0 2 2a2 2 0 0 0-2-2m18 6a4 4 0 1 1 4-4a4.005 4.005 0 0 1-4 4m0-6a2 2 0 1 0 2 2a2 2 0 0 0-2-2\"/><path fill=\"currentColor\" d=\"M18.5 15a3.5 3.5 0 1 1 3.5-3.5a3.504 3.504 0 0 1-3.5 3.5m0-5a1.5 1.5 0 1 0 1.5 1.5a1.5 1.5 0 0 0-1.5-1.5\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "carbon:add": {
        "svg": "<path fill=\"currentColor\" d=\"M17 15V8h-2v7H8v2h7v7h2v-7h7v-2z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "twemoji:desktop-computer": {
        "svg": "<path fill=\"#CCD6DD\" d=\"M36 22a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4h28a4 4 0 0 1 4 4z\"/><path fill=\"#5DADEC\" d=\"M4 4h28v18H4z\"/><path fill=\"#CCD6DD\" d=\"M13 26h10v6H13z\"/><path fill=\"#9AAAB4\" d=\"M13 26h10v2H13z\"/><path fill=\"#E1E8ED\" d=\"M36 33a3 3 0 0 0-3-3H3a3 3 0 1 0 0 6h30a3 3 0 0 0 3-3\"/><path fill=\"#F5F8FA\" d=\"M3 32h2v2H3zm4 0h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2zm4 0h2v2h-2zm4 0h2v2h-2zm4 0h2v2h-2zm4 0h2v2h-2z\"/>",
        "vb": "0 0 36 36",
        "strw": "0"
    },
    "twemoji:information": {
        "svg": "<path fill=\"#3B88C3\" d=\"M0 4a4 4 0 0 1 4-4h28a4 4 0 0 1 4 4v28a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z\"/><path fill=\"#FFF\" d=\"M20.512 8.071c0 1.395-1.115 2.573-2.511 2.573c-1.333 0-2.511-1.209-2.511-2.573c0-1.271 1.178-2.45 2.511-2.45c1.333.001 2.511 1.148 2.511 2.45m-4.744 6.728c0-1.488.931-2.481 2.232-2.481s2.232.992 2.232 2.481v11.906c0 1.488-.93 2.48-2.232 2.48s-2.232-.992-2.232-2.48z\"/>",
        "vb": "0 0 36 36",
        "strw": "0"
    },
    "carbon:collapse-categories": {
        "svg": "<path fill=\"currentColor\" d=\"M14 25h14v2H14zm-6.83 1l-2.58 2.58L6 30l4-4l-4-4l-1.42 1.41zM14 15h14v2H14zm-6.83 1l-2.58 2.58L6 20l4-4l-4-4l-1.42 1.41zM14 5h14v2H14zM7.17 6L4.59 8.58L6 10l4-4l-4-4l-1.42 1.41z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "carbon:expand-categories": {
        "svg": "<path fill=\"currentColor\" d=\"M20 26h6v2h-6zm0-8h8v2h-8zm0-8h10v2H20zm-5-6h2v24h-2zm-4.414-.041L7 7.249L3.412 3.958L2 5.373L7 10l5-4.627z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "carbon:airport-location": {
        "svg": "<path fill=\"currentColor\" d=\"M30 15h-6.07A8.01 8.01 0 0 0 17 8.07V2h-2v6.07A8.01 8.01 0 0 0 8.07 15H2v2h6.07A8.01 8.01 0 0 0 15 23.93V30h2v-6.07A8.01 8.01 0 0 0 23.93 17H30Zm-14 7a6 6 0 1 1 6-6a6.007 6.007 0 0 1-6 6\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "carbon:text-annotation-toggle": {
        "svg": "<path d=\"M29.537 13.76l-3.297-3.297a1.586 1.586 0 0 0-2.24 0L10 24.467V30h5.533l14.004-14a1.586 1.586 0 0 0 0-2.24zM14.704 28H12v-2.704l9.44-9.441l2.705 2.704zM25.56 17.145l-2.704-2.704l2.267-2.267l2.704 2.704z\" fill=\"currentColor\"/><path d=\"M11 17h2v-7h3V8H8v2h3v7z\" fill=\"currentColor\"/><path d=\"M8 20H4V4h16v4h2V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h4z\" fill=\"currentColor\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "carbon:folder-add": {
        "svg": "<path fill=\"currentColor\" d=\"M26 20h-2v4h-4v2h4v4h2v-4h4v-2h-4z\"/><path fill=\"currentColor\" d=\"M28 8H16l-3.4-3.4c-.4-.4-.9-.6-1.4-.6H4c-1.1 0-2 .9-2 2v20c0 1.1.9 2 2 2h14v-2H4V6h7.2l3.4 3.4l.6.6H28v8h2v-8c0-1.1-.9-2-2-2\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "fc-planner": {
        "svg": "<path fill=\"#CFD8DC\" d=\"M5 38V14h38v24c0 2.2-1.8 4-4 4H9c-2.2 0-4-1.8-4-4\"/><path fill=\"#F44336\" d=\"M43 10v6H5v-6c0-2.2 1.8-4 4-4h30c2.2 0 4 1.8 4 4\"/><g fill=\"#B71C1C\"><circle cx=\"33\" cy=\"10\" r=\"3\"/><circle cx=\"15\" cy=\"10\" r=\"3\"/></g><path fill=\"#B0BEC5\" d=\"M33 3c-1.1 0-2 .9-2 2v5c0 1.1.9 2 2 2s2-.9 2-2V5c0-1.1-.9-2-2-2M15 3c-1.1 0-2 .9-2 2v5c0 1.1.9 2 2 2s2-.9 2-2V5c0-1.1-.9-2-2-2m-2 18h6v6h-6zm8 0h6v6h-6zm8 0h6v6h-6zm-16 8h6v6h-6zm8 0h6v6h-6z\"/><path fill=\"#F44336\" d=\"M29 29h6v6h-6z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc-businessman": {
        "svg": "<path fill=\"#FF9800\" d=\"m24 37l-5-6v-6h10v6z\"/><g fill=\"#FFA726\"><circle cx=\"33\" cy=\"19\" r=\"2\"/><circle cx=\"15\" cy=\"19\" r=\"2\"/></g><path fill=\"#FFB74D\" d=\"M33 13c0-7.6-18-5-18 0v7c0 5 4 9 9 9s9-4 9-9z\"/><path fill=\"#424242\" d=\"M24 4c-6.1 0-10 4.9-10 11v2.3l2 1.7v-5l12-4l4 4v5l2-1.7V15c0-4-1-8-6-9l-1-2z\"/><g fill=\"#784719\"><circle cx=\"28\" cy=\"19\" r=\"1\"/><circle cx=\"20\" cy=\"19\" r=\"1\"/></g><path fill=\"#fff\" d=\"m24 43l-5-12l5 1l5-1z\"/><path fill=\"#D32F2F\" d=\"m23 35l-.7 4.5l1.7 4l1.7-4L25 35l1-1l-2-2l-2 2z\"/><path fill=\"#546E7A\" d=\"m29 31l-5 12l-5-12S8 33 8 44h32c0-11-11-13-11-13\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc-businesswoman": {
        "svg": "<path fill=\"#BF360C\" d=\"M16 15h16v18H16z\"/><path fill=\"#78909C\" d=\"M40 44H8c0-11 11-13 11-13h10s11 2 11 13\"/><path fill=\"#FF9800\" d=\"M24 37c-2.2 0-5-6-5-6v-6h10v6s-2.8 6-5 6\"/><path fill=\"#FFB74D\" d=\"M33 14c0-7.6-18-5-18 0v7c0 5 4 9 9 9s9-4 9-9z\"/><path fill=\"#FF5722\" d=\"M24 4C17.9 4 9 7.4 9 27.3l7 4.7V19l12-7l4 5v15l7-6c0-4-.7-20-11-20l-1-2z\"/><path fill=\"#FFB74D\" d=\"M24 38c-4.4 0-5-7-5-7s2.5 4 5 4s5-4 5-4s-.6 7-5 7\"/><circle cx=\"28\" cy=\"21\" r=\"1\" fill=\"#784719\"/><circle cx=\"20\" cy=\"21\" r=\"1\" fill=\"#784719\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "cb-chevron-right": {
        "svg": "<path fill=\"currentColor\" d=\"M22 16L12 26l-1.4-1.4l8.6-8.6l-8.6-8.6L12 6z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "pepicons-pencil:photo-camera": {
        "svg": "<g fill=\"currentColor\" fill-rule=\"evenodd\" clip-rule=\"evenodd\"><path d=\"M6.172 5.232L5.762 6.5H4.5A2.5 2.5 0 0 0 2 9v6a2.5 2.5 0 0 0 2.5 2.5h11A2.5 2.5 0 0 0 18 15V9a2.5 2.5 0 0 0-2.5-2.5h-1.263l-.409-1.268a2.5 2.5 0 0 0-2.38-1.732H8.552a2.5 2.5 0 0 0-2.38 1.732M4.5 7.5h1.99l.633-1.96A1.5 1.5 0 0 1 8.551 4.5h2.898a1.5 1.5 0 0 1 1.427 1.04l.633 1.96H15.5A1.5 1.5 0 0 1 17 9v6a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 3 15V9a1.5 1.5 0 0 1 1.5-1.5\"/><path d=\"M7 11.5a3 3 0 1 0 6 0a3 3 0 0 0-6 0m5 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0\"/></g>",
        "vb": "0 0 20 20",
        "strw": "0"
    },
    "material-symbols-light:scan-delete-outline-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M6 4v4zv16zm.616 17q-.691 0-1.153-.462T5 19.385V4.615q0-.69.463-1.152T6.616 3h7.213q.323 0 .628.13t.522.349L18.52 7.02q.217.218.348.522t.131.628v4.006q0 .214-.143.357t-.357.143t-.357-.143t-.143-.357V8h-2.962q-.44 0-.739-.299T14 6.961V4H6.616q-.231 0-.424.192T6 4.615v14.77q0 .23.192.423t.423.192h5.835q.214 0 .357.143t.143.357t-.143.357t-.357.143zM18 18.79l-1.766 1.74q-.14.141-.331.135q-.191-.007-.337-.153q-.141-.14-.141-.345q0-.203.14-.344l1.747-1.746l-1.766-1.765q-.14-.14-.13-.345q.009-.203.15-.344t.343-.14q.204 0 .345.14L18 17.389l1.766-1.766q.14-.14.34-.133t.348.152q.14.14.14.335t-.14.335l-1.74 1.765l1.74 1.765q.14.14.133.342q-.006.2-.153.347q-.14.14-.334.14t-.334-.14z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "material-symbols-light:note-add-outline-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M11.5 14.5V17q0 .213.144.356t.357.144t.356-.144T12.5 17v-2.5H15q.213 0 .356-.144t.144-.357t-.144-.356T15 13.5h-2.5V11q0-.213-.144-.356t-.357-.144t-.356.144T11.5 11v2.5H9q-.213 0-.356.144t-.144.357t.144.356T9 14.5zM6.616 21q-.691 0-1.153-.462T5 19.385V4.615q0-.69.463-1.152T6.616 3h7.213q.331 0 .632.13t.518.349L18.52 7.02q.217.218.348.518t.131.632v11.214q0 .69-.463 1.153T17.385 21zM14 7.2V4H6.616q-.231 0-.424.192T6 4.615v14.77q0 .23.192.423t.423.192h10.77q.23 0 .423-.192t.192-.424V8h-3.2q-.34 0-.57-.23T14 7.2M6 4v4zv16z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "flat-color-icons:overtime": {
        "svg": "<path fill=\"#CFD8DC\" d=\"M12 40V20h32v20c0 2.2-1.8 4-4 4H16c-2.2 0-4-1.8-4-4\"/><path fill=\"#78909C\" d=\"M44 16v6H12v-6c0-2.2 1.8-4 4-4h24c2.2 0 4 1.8 4 4\"/><g fill=\"#37474F\"><circle cx=\"37\" cy=\"16\" r=\"3\"/><circle cx=\"20\" cy=\"16\" r=\"3\"/></g><path fill=\"#B0BEC5\" d=\"M37 10c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2s2-.9 2-2v-4c0-1.1-.9-2-2-2m-17 0c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2s2-.9 2-2v-4c0-1.1-.9-2-2-2\"/><path fill=\"#90A4AE\" d=\"M32 34h4v4h-4zm-6 0h4v4h-4zm-6 0h4v4h-4zm12-6h4v4h-4zm-6 0h4v4h-4zm-6 0h4v4h-4z\"/><circle cx=\"16\" cy=\"15\" r=\"12\" fill=\"#F44336\"/><circle cx=\"16\" cy=\"15\" r=\"9\" fill=\"#eee\"/><path d=\"M15 8h2v7h-2z\"/><path d=\"m20.518 18.1l-1.343 1.344l-3.818-3.818l1.344-1.343z\"/><circle cx=\"16\" cy=\"15\" r=\"1.5\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fx:close": {
        "svg": "<path fill=\"currentColor\" d=\"M17.414 16L24 9.414L22.586 8L16 14.586L9.414 8L8 9.414L14.586 16L8 22.586L9.414 24L16 17.414L22.586 24L24 22.586z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "game-icons:family-tree": {
        "svg": "<path fill=\"currentColor\" d=\"M25.01 49v46H103V49zM153 49v46h78V49zm128 0v46h78V49zm128 0v46h78V49zM55.01 113v64H119v46h18v-46h64v-64h-18v46H73.01v-46zM311 113v64h64v46h18v-46h64v-64h-18v46H329v-46zM89.01 241v46H167v-46zM345 241v46h78v-46zm-226 64v48h128v46h18v-46h128v-48h-18v30H137v-30zm98 112v46h78v-46z\"/>",
        "vb": "0 0 512 512",
        "strw": "0"
    },
    "carbon:link": {
        "svg": "<path fill=\"currentColor\" d=\"M29.25 6.76a6 6 0 0 0-8.5 0l1.42 1.42a4 4 0 1 1 5.67 5.67l-8 8a4 4 0 1 1-5.67-5.66l1.41-1.42l-1.41-1.42l-1.42 1.42a6 6 0 0 0 0 8.5A6 6 0 0 0 17 25a6 6 0 0 0 4.27-1.76l8-8a6 6 0 0 0-.02-8.48\"/><path fill=\"currentColor\" d=\"M4.19 24.82a4 4 0 0 1 0-5.67l8-8a4 4 0 0 1 5.67 0A3.94 3.94 0 0 1 19 14a4 4 0 0 1-1.17 2.85L15.71 19l1.42 1.42l2.12-2.12a6 6 0 0 0-8.51-8.51l-8 8a6 6 0 0 0 0 8.51A6 6 0 0 0 7 28a6.07 6.07 0 0 0 4.28-1.76l-1.42-1.42a4 4 0 0 1-5.67 0\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "carbon:edit": {
        "svg": "<path fill=\"currentColor\" d=\"M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4zm-5-5L24 7.6l-3 3L17.4 7zM6 22v-3.6l10-10l3.6 3.6l-10 10z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "material-symbols-light:delete-forever-outline-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M7.616 20q-.672 0-1.144-.472T6 18.385V6h-.5q-.213 0-.357-.143T5 5.5t.143-.357T5.5 5H9q0-.31.23-.54t.54-.23h4.46q.31 0 .54.23T15 5h3.5q.214 0 .357.143T19 5.5t-.143.357T18.5 6H18v12.385q0 .67-.472 1.143q-.472.472-1.143.472zM17 6H7v12.385q0 .269.173.442t.443.173h8.769q.269 0 .442-.173t.173-.442zM7 6v13zm5 7.208l2.246 2.246q.14.14.344.15t.364-.15t.16-.354t-.16-.354L12.708 12.5l2.246-2.246q.14-.14.15-.344t-.15-.364t-.354-.16t-.354.16L12 11.792L9.754 9.546q-.14-.14-.344-.15t-.364.15t-.16.354t.16.354l2.246 2.246l-2.246 2.246q-.14.14-.15.344t.15.364t.354.16t.354-.16z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "material-symbols-light:open-in-new-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M5.616 20q-.691 0-1.153-.462T4 18.384V5.616q0-.691.463-1.153T5.616 4h5.115q.213 0 .357.143t.143.357t-.143.357T10.73 5H5.616q-.231 0-.424.192T5 5.616v12.769q0 .23.192.423t.423.192h12.77q.23 0 .423-.192t.192-.423v-5.116q0-.213.143-.357t.357-.143t.357.143t.143.357v5.116q0 .69-.462 1.152T18.384 20zM19 5.708l-8.908 8.908q-.14.14-.344.15t-.363-.15t-.16-.354t.16-.354L18.292 5H14.5q-.213 0-.357-.143T14 4.5t.143-.357T14.5 4h4.692q.349 0 .578.23t.23.578V9.5q0 .214-.143.357T19.5 10t-.357-.143T19 9.5z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "mdi:dots-vertical": {
        "svg": "<path fill=\"currentColor\" d=\"M12 16a2 2 0 0 1 2 2a2 2 0 0 1-2 2a2 2 0 0 1-2-2a2 2 0 0 1 2-2m0-6a2 2 0 0 1 2 2a2 2 0 0 1-2 2a2 2 0 0 1-2-2a2 2 0 0 1 2-2m0-6a2 2 0 0 1 2 2a2 2 0 0 1-2 2a2 2 0 0 1-2-2a2 2 0 0 1 2-2\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "bx:dots-vertical-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M12 10c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-6c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 12c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "fc:multiple-cameras": {
        "svg": "<path fill=\"#455A64\" d=\"M42 41H12c-2.2 0-4-1.8-4-4V17c0-2.2 1.8-4 4-4h30c2.2 0 4 1.8 4 4v20c0 2.2-1.8 4-4 4\"/><path fill=\"#78909C\" d=\"M36 36H6c-2.2 0-4-1.8-4-4V12c0-2.2 1.8-4 4-4h30c2.2 0 4 1.8 4 4v20c0 2.2-1.8 4-4 4\"/><circle cx=\"26\" cy=\"22\" r=\"10\" fill=\"#455A64\"/><circle cx=\"26\" cy=\"22\" r=\"7\" fill=\"#42A5F5\"/><path fill=\"#90CAF9\" d=\"M29.7 19.7c-1-1.1-2.3-1.7-3.7-1.7s-2.8.6-3.7 1.7c-.4.4-.3 1 .1 1.4s1 .3 1.4-.1c1.2-1.3 3.3-1.3 4.5 0c.*******.7.3s.5-.1.7-.3c.4-.3.4-.9 0-1.3\"/><path fill=\"#ADD8FB\" d=\"M6 12h6v3H6z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:android-os": {
        "svg": "<path fill=\"#7CB342\" d=\"M12 29.001a2 2 0 0 1-4 0v-9a2 2 0 0 1 4 0zm28 0a2 2 0 0 1-4 0v-9a2 2 0 0 1 4 0zM22 40a2 2 0 0 1-4 0v-9a2 2 0 0 1 4 0zm8 0a2 2 0 0 1-4 0v-9a2 2 0 0 1 4 0z\"/><path fill=\"#7CB342\" d=\"M14 18.001V33a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V18.001zM24 8c-6 0-9.655 3.645-10 8h20c-.346-4.355-4-8-10-8m-4 5.598a1 1 0 1 1 0-2a1 1 0 0 1 0 2m8 0a1 1 0 1 1 0-2a1 1 0 0 1 0 2\"/><path fill=\"none\" stroke=\"#7CB342\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"m30 7l-1.666 2.499M18 7l1.333 2.082\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:puzzle": {
        "svg": "<path fill=\"#8BC34A\" d=\"M39 15c0-2.2-1.8-4-4-4h-6c-.7 0-1.1-.8-.7-1.4c.6-1 .9-2.2.6-3.5c-.4-2-1.9-3.6-3.8-4C21.8 1.4 19 3.9 19 7c0 1 .3 1.8.7 2.6c.4.6 0 1.4-.8 1.4h-6c-2.2 0-4 1.8-4 4v7c0 .7.8 1.1 1.4.7c1-.6 2.2-.9 3.5-.6c2 .4 3.6 1.9 4 3.8c.7 3.2-1.8 6.1-4.9 6.1c-1 0-1.8-.3-2.6-.7c-.5-.4-1.3 0-1.3.7v6c0 2.2 1.8 4 4 4h22c2.2 0 4-1.8 4-4z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:todo-list": {
        "svg": "<path fill=\"#3F51B5\" d=\"m17.8 18.1l-7.4 7.3l-4.2-4.1L4 23.5l6.4 6.4l9.6-9.6zm0-13l-7.4 7.3l-4.2-4.1L4 10.5l6.4 6.4L20 7.3zm0 26l-7.4 7.3l-4.2-4.1L4 36.5l6.4 6.4l9.6-9.6z\"/><path fill=\"#90CAF9\" d=\"M24 22h20v4H24zm0-13h20v4H24zm0 26h20v4H24z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:parallel-tasks": {
        "svg": "<path fill=\"#90CAF9\" d=\"M36 13V9H22v13h-9v4h9v13h14v-4H26v-9h10v-4H26v-9z\"/><path fill=\"#D81B60\" d=\"M6 17h10v14H6z\"/><path fill=\"#2196F3\" d=\"M32 6h10v10H32zm0 26h10v10H32zm0-13h10v10H32z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:currency-exchange": {
        "svg": "<circle cx=\"18\" cy=\"18\" r=\"15\" fill=\"#3F51B5\"/><path fill=\"#FFF59D\" d=\"M20.3 16v1.7h-3.8v1.4h3.8v1.7h-3.8c0 .6.1 1.2.3 1.6s.4.8.7 1c.3.3.7.4 1.1.6c.4.1.9.2 1.4.2c.4 0 .7 0 1.1-.1s.7-.1 1-.3l.4 2.7c-.4.1-.9.2-1.4.2c-.5.1-1 .1-1.5.1c-.9 0-1.8-.1-2.6-.4c-.8-.2-1.5-.6-2-1.1c-.6-.5-1-1.1-1.4-1.9c-.3-.7-.5-1.6-.5-2.6h-1.9v-1.7h1.9v-1.4h-1.9V16h1.9c.1-1 .3-1.8.6-2.6c.4-.7.8-1.4 1.4-1.9s1.3-.9 2.1-1.1c.8-.3 1.7-.4 2.6-.4c.4 0 .9 0 1.3.1s.9.1 1.3.3l-.4 2.7c-.3-.1-.6-.2-1-.3s-.7-.1-1.1-.1c-.5 0-1 .1-1.4.2s-.8.3-1 .6c-.3.3-.5.6-.7 1s-.3.9-.3 1.5z\"/><circle cx=\"30\" cy=\"30\" r=\"15\" fill=\"#4CAF50\"/><path fill=\"#fff\" d=\"M28.4 27c.1.2.2.4.4.6s.4.4.7.5c.3.2.7.3 1.1.5c.7.3 1.4.6 2 .9s1.1.7 1.5 1.1s.8.9 1 1.4s.4 1.2.4 1.9s-.1 1.3-.3 1.8s-.5 1-.9 1.4s-.9.7-1.4.9c-.6.2-1.2.4-1.8.5v2.2h-1.8v-2.2c-.6-.1-1.2-.2-1.8-.4s-1.1-.5-1.5-1c-.5-.4-.8-1-1.1-1.6s-.4-1.4-.4-2.3h3.3c0 .5.1 1 .2 1.3c.1.4.3.6.6.9c.2.2.5.4.8.5s.6.1.9.1c.4 0 .7 0 .9-.1c.3-.1.5-.2.7-.4s.3-.4.4-.6s.1-.5.1-.8s0-.6-.1-.8s-.2-.5-.4-.7s-.4-.4-.7-.5c-.3-.2-.7-.3-1.1-.5c-.7-.3-1.4-.6-2-.9s-1.1-.7-1.5-1.1s-.8-.9-1-1.4s-.4-1.2-.4-1.9c0-.6.1-1.2.3-1.7s.5-1 .9-1.4s.9-.7 1.4-1c.5-.2 1.2-.4 1.8-.5v-2.4h1.8v2.4q.9.15 1.8.6c.5.3 1 .6 1.3 1.1c.4.4.7 1 .9 1.6s.3 1.3.3 2h-3.3c0-.9-.2-1.6-.6-2s-.9-.7-1.5-.7c-.3 0-.6.1-.9.2c-.2.1-.4.2-.6.4q-.3.3-.3.6c-.1.2-.1.5-.1.8c-.1.2 0 .5 0 .7\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:conference-call": {
        "svg": "<circle cx=\"12\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/><path fill=\"#455A64\" d=\"M2 34.7s2.8-6.3 10-6.3s10 6.3 10 6.3V38H2zm44 0s-2.8-6.3-10-6.3s-10 6.3-10 6.3V38h20z\"/><circle cx=\"24\" cy=\"17\" r=\"6\" fill=\"#FFB74D\"/><path fill=\"#607D8B\" d=\"M36 34.1s-3.3-7.5-12-7.5s-12 7.5-12 7.5V38h24z\"/><circle cx=\"36\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/><circle cx=\"12\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/><circle cx=\"36\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:grid": {
        "svg": "<path fill=\"#90CAF9\" d=\"M7 7v34h34V7zm32 8h-6V9h6zm-14 0V9h6v6zm6 2v6h-6v-6zm-8-2h-6V9h6zm0 2v6h-6v-6zm-8 6H9v-6h6zm0 2v6H9v-6zm2 0h6v6h-6zm6 8v6h-6v-6zm2 0h6v6h-6zm0-2v-6h6v6zm8-6h6v6h-6zm0-2v-6h6v6zM15 9v6H9V9zM9 33h6v6H9zm24 6v-6h6v6z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:data-sheet": {
        "svg": "<path fill=\"#90CAF9\" d=\"M39 16v7h-6v-7h-2v7h-6v-7h-2v7h-7v2h7v6h-7v2h7v6h-7v2h25V16zm0 9v6h-6v-6zm-14 0h6v6h-6zm0 8h6v6h-6zm8 6v-6h6v6z\"/><path fill=\"#00BCD4\" d=\"M40 8H8v32h8V16h24z\"/><path fill=\"#0097A7\" d=\"M7 7v34h10V17h24V7zm2 16v-6h6v6zm6 2v6H9v-6zm2-16h6v6h-6zm8 0h6v6h-6zM15 9v6H9V9zM9 39v-6h6v6zm30-24h-6V9h6z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc:survey": {
        "svg": "<path fill=\"#455A64\" d=\"M36 4H26c0 1.1-.9 2-2 2s-2-.9-2-2H12C9.8 4 8 5.8 8 8v32c0 2.2 1.8 4 4 4h24c2.2 0 4-1.8 4-4V8c0-2.2-1.8-4-4-4\"/><path fill=\"#fff\" d=\"M36 41H12c-.6 0-1-.4-1-1V8c0-.6.4-1 1-1h24c.6 0 1 .4 1 1v32c0 .6-.4 1-1 1\"/><g fill=\"#90A4AE\"><path d=\"M26 4c0 1.1-.9 2-2 2s-2-.9-2-2h-7v4c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4z\"/><path d=\"M24 0c-2.2 0-4 1.8-4 4s1.8 4 4 4s4-1.8 4-4s-1.8-4-4-4m0 6c-1.1 0-2-.9-2-2s.9-2 2-2s2 .9 2 2s-.9 2-2 2\"/></g><path fill=\"#CFD8DC\" d=\"M21 20h12v2H21zm-6-1h4v4h-4z\"/><path fill=\"#03A9F4\" d=\"M21 29h12v2H21zm-6-1h4v4h-4z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:planner": {
        "svg": "<path fill=\"#CFD8DC\" d=\"M5 38V14h38v24c0 2.2-1.8 4-4 4H9c-2.2 0-4-1.8-4-4\"/><path fill=\"#F44336\" d=\"M43 10v6H5v-6c0-2.2 1.8-4 4-4h30c2.2 0 4 1.8 4 4\"/><g fill=\"#B71C1C\"><circle cx=\"33\" cy=\"10\" r=\"3\"/><circle cx=\"15\" cy=\"10\" r=\"3\"/></g><path fill=\"#B0BEC5\" d=\"M33 3c-1.1 0-2 .9-2 2v5c0 1.1.9 2 2 2s2-.9 2-2V5c0-1.1-.9-2-2-2M15 3c-1.1 0-2 .9-2 2v5c0 1.1.9 2 2 2s2-.9 2-2V5c0-1.1-.9-2-2-2m-2 18h6v6h-6zm8 0h6v6h-6zm8 0h6v6h-6zm-16 8h6v6h-6zm8 0h6v6h-6z\"/><path fill=\"#F44336\" d=\"M29 29h6v6h-6z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:multiple-smartphones": {
        "svg": "<path fill=\"#455A64\" d=\"M4 31V8c0-2.2 1.8-4 4-4h12c2.2 0 4 1.8 4 4v23c0 2.2-1.8 4-4 4H8c-2.2 0-4-1.8-4-4\"/><path fill=\"#BBDEFB\" d=\"M20 7H8c-.6 0-1 .4-1 1v21c0 .6.4 1 1 1h12c.6 0 1-.4 1-1V8c0-.6-.4-1-1-1\"/><circle cx=\"14\" cy=\"32.5\" r=\"1.5\" fill=\"#37474F\"/><path fill=\"#546E7A\" d=\"M14 36V13c0-2.2 1.8-4 4-4h12c2.2 0 4 1.8 4 4v23c0 2.2-1.8 4-4 4H18c-2.2 0-4-1.8-4-4\"/><path fill=\"#BBDEFB\" d=\"M30 12H18c-.6 0-1 .4-1 1v21c0 .6.4 1 1 1h12c.6 0 1-.4 1-1V13c0-.6-.4-1-1-1\"/><circle cx=\"24\" cy=\"37.5\" r=\"1.5\" fill=\"#37474F\"/><path fill=\"#E38939\" d=\"M24 40V18c0-2.2 1.8-4 4-4h12c2.2 0 4 1.8 4 4v22c0 2.2-1.8 4-4 4H28c-2.2 0-4-1.8-4-4\"/><path fill=\"#FFF3E0\" d=\"M40 17H28c-.6 0-1 .4-1 1v20c0 .6.4 1 1 1h12c.6 0 1-.4 1-1V18c0-.6-.4-1-1-1\"/><circle cx=\"34\" cy=\"41.5\" r=\"1.5\" fill=\"#A6642A\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:businessman": {
        "svg": "<path fill=\"#FF9800\" d=\"m24 37l-5-6v-6h10v6z\"/><g fill=\"#FFA726\"><circle cx=\"33\" cy=\"19\" r=\"2\"/><circle cx=\"15\" cy=\"19\" r=\"2\"/></g><path fill=\"#FFB74D\" d=\"M33 13c0-7.6-18-5-18 0v7c0 5 4 9 9 9s9-4 9-9z\"/><path fill=\"#424242\" d=\"M24 4c-6.1 0-10 4.9-10 11v2.3l2 1.7v-5l12-4l4 4v5l2-1.7V15c0-4-1-8-6-9l-1-2z\"/><g fill=\"#784719\"><circle cx=\"28\" cy=\"19\" r=\"1\"/><circle cx=\"20\" cy=\"19\" r=\"1\"/></g><path fill=\"#fff\" d=\"m24 43l-5-12l5 1l5-1z\"/><path fill=\"#D32F2F\" d=\"m23 35l-.7 4.5l1.7 4l1.7-4L25 35l1-1l-2-2l-2 2z\"/><path fill=\"#546E7A\" d=\"m29 31l-5 12l-5-12S8 33 8 44h32c0-11-11-13-11-13\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:businesswoman": {
        "svg": "<path fill=\"#BF360C\" d=\"M16 15h16v18H16z\"/><path fill=\"#78909C\" d=\"M40 44H8c0-11 11-13 11-13h10s11 2 11 13\"/><path fill=\"#FF9800\" d=\"M24 37c-2.2 0-5-6-5-6v-6h10v6s-2.8 6-5 6\"/><path fill=\"#FFB74D\" d=\"M33 14c0-7.6-18-5-18 0v7c0 5 4 9 9 9s9-4 9-9z\"/><path fill=\"#FF5722\" d=\"M24 4C17.9 4 9 7.4 9 27.3l7 4.7V19l12-7l4 5v15l7-6c0-4-.7-20-11-20l-1-2z\"/><path fill=\"#FFB74D\" d=\"M24 38c-4.4 0-5-7-5-7s2.5 4 5 4s5-4 5-4s-.6 7-5 7\"/><circle cx=\"28\" cy=\"21\" r=\"1\" fill=\"#784719\"/><circle cx=\"20\" cy=\"21\" r=\"1\" fill=\"#784719\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:combo-chart": {
        "svg": "<path fill=\"#00BCD4\" d=\"M37 18h6v24h-6zm-8 8h6v16h-6zm-8-4h6v20h-6zm-8 10h6v10h-6zm-8-4h6v14H5z\"/><g fill=\"#3F51B5\"><circle cx=\"8\" cy=\"16\" r=\"3\"/><circle cx=\"16\" cy=\"18\" r=\"3\"/><circle cx=\"24\" cy=\"11\" r=\"3\"/><circle cx=\"32\" cy=\"13\" r=\"3\"/><circle cx=\"40\" cy=\"9\" r=\"3\"/><path d=\"m39.1 7.2l-7.3 3.7l-8.3-2.1l-8 7l-7-1.7l-1 3.8l9 2.3l8-7l7.7 1.9l8.7-4.3z\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:sports-mode": {
        "svg": "<circle cx=\"28\" cy=\"9\" r=\"5\" fill=\"#FF9800\"/><path fill=\"#00796B\" d=\"m29 27.3l-9.2-4.1c-1-.5-1.5 1-2 2s-4.1 7.2-3.8 8.3c.3.9 1.1 1.4 1.9 1.4c.2 0 .4 0 .6-.1L28.8 31c.8-.2 1.4-1 1.4-1.8s-.5-1.6-1.2-1.9\"/><path fill=\"#009688\" d=\"m26.8 15.2l-2.2-1c-1.3-.6-2.9 0-3.5 1.3L9.2 41.1c-.5 1 0 2.2 1 2.7c.3.1.6.2.9.2c.8 0 1.5-.4 1.8-1.1c0 0 9.6-13.3 10.4-14.9s4.9-9.3 4.9-9.3c.5-1.3 0-2.9-1.4-3.5\"/><path fill=\"#FF9800\" d=\"M40.5 15.7c-.7-.8-2-1-2.8-.3l-5 4.2l-6.4-3.5c-1.1-.6-2.6-.4-3.3.9c-.8 1.3-.4 2.9.8 3.4l8.3 3.4c.3.1.6.2.9.2c.5 0 .9-.2 1.3-.5l6-5c.8-.7.9-1.9.2-2.8m-28.8 7.4l3.4-5.1l4.6.6l1.5-3.1c.4-.9 1.2-1.4 2.1-1.5h-9.2c-.7 0-1.3.3-1.7.9l-4 6c-.6.9-.4 2.2.6 2.8c.2.2.6.3 1 .3c.6 0 1.3-.3 1.7-.9\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "clarity:settings-line": {
        "svg": "<path fill=\"currentColor\" d=\"M18.1 11c-3.9 0-7 3.1-7 7s3.1 7 7 7s7-3.1 7-7s-3.1-7-7-7m0 12c-2.8 0-5-2.2-5-5s2.2-5 5-5s5 2.2 5 5s-2.2 5-5 5\" class=\"clr-i-outline clr-i-outline-path-1\"/><path fill=\"currentColor\" d=\"m32.8 14.7l-2.8-.9l-.6-1.5l1.4-2.6c.3-.6.2-1.4-.3-1.9l-2.4-2.4c-.5-.5-1.3-.6-1.9-.3l-2.6 1.4l-1.5-.6l-.9-2.8C21 2.5 20.4 2 19.7 2h-3.4c-.7 0-1.3.5-1.4 1.2L14 6c-.6.1-1.1.3-1.6.6L9.8 5.2c-.6-.3-1.4-.2-1.9.3L5.5 7.9c-.5.5-.6 1.3-.3 1.9l1.3 2.5c-.2.5-.4 1.1-.6 1.6l-2.8.9c-.6.2-1.1.8-1.1 1.5v3.4c0 .7.5 1.3 1.2 1.5l2.8.9l.6 1.5l-1.4 2.6c-.3.6-.2 1.4.3 1.9l2.4 2.4c.5.5 1.3.6 1.9.3l2.6-1.4l1.5.6l.9 2.9c.2.6.8 1.1 1.5 1.1h3.4c.7 0 1.3-.5 1.5-1.1l.9-2.9l1.5-.6l2.6 1.4c.6.3 1.4.2 1.9-.3l2.4-2.4c.5-.5.6-1.3.3-1.9l-1.4-2.6l.6-1.5l2.9-.9c.6-.2 1.1-.8 1.1-1.5v-3.4c0-.7-.5-1.4-1.2-1.6m-.8 4.7l-3.6 1.1l-.1.5l-.9 2.1l-.3.5l1.8 3.3l-2 2l-3.3-1.8l-.5.3q-1.05.6-2.1.9l-.5.1l-1.1 3.6h-2.8l-1.1-3.6l-.5-.1l-2.1-.9l-.5-.3l-3.3 1.8l-2-2l1.8-3.3l-.3-.5Q8 22.05 7.7 21l-.1-.5L4 19.4v-2.8l3.4-1l.2-.5c.2-.8.5-1.5.9-2.2l.3-.5l-1.7-3.3l2-2l3.2 1.8l.5-.3c.7-.4 1.4-.7 2.2-.9l.5-.2L16.6 4h2.8l1.1 3.5l.5.2q1.05.3 2.1.9l.5.3l3.3-1.8l2 2l-1.8 3.3l.3.5q.6 1.05.9 2.1l.1.5l3.6 1.1z\" class=\"clr-i-outline clr-i-outline-path-2\"/><path fill=\"none\" d=\"M0 0h36v36H0z\"/>",
        "vb": "0 0 36 36",
        "strw": "0"
    },
    "carbon:close": {
        "svg": "<path fill=\"currentColor\" d=\"M17.414 16L24 9.414L22.586 8L16 14.586L9.414 8L8 9.414L14.586 16L8 22.586L9.414 24L16 17.414L22.586 24L24 22.586z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "carbon:document-pdf": {
        "svg": "<path fill=\"currentColor\" d=\"M30 18v-2h-6v10h2v-4h3v-2h-3v-2zm-11 8h-4V16h4a3.003 3.003 0 0 1 3 3v4a3.003 3.003 0 0 1-3 3m-2-2h2a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2zm-6-8H6v10h2v-3h3a2.003 2.003 0 0 0 2-2v-3a2 2 0 0 0-2-2m-3 5v-3h3l.001 3z\"/><path fill=\"currentColor\" d=\"M22 14v-4a.91.91 0 0 0-.3-.7l-7-7A.9.9 0 0 0 14 2H4a2.006 2.006 0 0 0-2 2v24a2 2 0 0 0 2 2h16v-2H4V4h8v6a2.006 2.006 0 0 0 2 2h6v2Zm-8-4V4.4l5.6 5.6Z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "bs:share": {
        "svg": "<path fill=\"currentColor\" d=\"M13.5 1a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3M11 2.5a2.5 2.5 0 1 1 .603 1.628l-6.718 3.12a2.5 2.5 0 0 1 0 1.504l6.718 3.12a2.5 2.5 0 1 1-.488.876l-6.718-3.12a2.5 2.5 0 1 1 0-3.256l6.718-3.12A2.5 2.5 0 0 1 11 2.5m-8.5 4a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3m11 5.5a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3\"/>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "vscode-icons:file-type-html": {
        "svg": "<path fill=\"#e44f26\" d=\"M5.902 27.201L3.655 2h24.69l-2.25 25.197L15.985 30z\"/><path fill=\"#f1662a\" d=\"m16 27.858l8.17-2.265l1.922-21.532H16z\"/><path fill=\"#ebebeb\" d=\"M16 13.407h-4.09l-.282-3.165H16V7.151H8.25l.074.83l.759 8.517H16zm0 8.027l-.014.004l-3.442-.929l-.22-2.465H9.221l.433 4.852l6.332 1.758l.014-.004z\"/><path fill=\"#fff\" d=\"M15.989 13.407v3.091h3.806l-.358 4.009l-3.448.93v3.216l6.337-1.757l.046-.522l.726-8.137l.076-.83zm0-6.256v3.091h7.466l.062-.694l.141-1.567l.074-.83z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "fxemoji:meridianglobe": {
        "svg": "<path fill=\"#0096D1\" d=\"M425.393 86.607C380.146 41.361 319.988 16.442 256 16.442S131.854 41.361 86.607 86.607S16.442 192.012 16.442 256S41.36 380.146 86.607 425.393S192.012 495.558 256 495.558s124.146-24.918 169.393-70.165S495.558 319.988 495.558 256S470.64 131.854 425.393 86.607M386.027 242.5c-1.141-38.785-7.187-75.873-17.566-108.605c16.922-4.791 32.653-10.738 47.349-17.882c30.041 34.253 49.265 78.207 52.307 126.487zM242.5 466.638c-20.989-5.949-40.869-25.655-57.048-56.984a228 228 0 0 1-5.844-12.219c11.593-2.202 23.68-3.935 36.277-5.158a428 428 0 0 1 26.615-1.739zm27-76.15c21.326.656 42.336 2.977 62.887 6.956a228 228 0 0 1-5.839 12.209c-16.179 31.329-36.059 51.036-57.048 56.984zm-27-26.963c-9.7.314-19.444.927-29.225 1.877c-15.111 1.467-29.588 3.622-43.422 6.429c-9.922-30.536-15.727-65.521-16.87-102.331H242.5zM152.984 242.5c1.143-36.816 6.95-71.805 16.874-102.345c23.712 4.87 47.989 7.663 72.642 8.375v93.97zM242.5 121.523c-21.327-.657-42.338-2.984-62.891-6.959a229 229 0 0 1 5.843-12.218c16.179-31.33 36.058-51.037 57.048-56.985zm27-76.161c20.989 5.948 40.869 25.655 57.048 56.985a228 228 0 0 1 5.871 12.282c-10.417 1.958-21.302 3.531-32.689 4.73a430 430 0 0 1-30.229 2.096zm81.038 44.597c-6.618-12.816-13.906-24.061-21.732-33.669c24.658 9.017 47.19 22.48 66.629 39.411c-11.359 4.975-23.438 9.21-36.287 12.755c-2.686-6.4-5.554-12.579-8.61-18.497m-189.076 0c-3.041 5.888-5.896 12.035-8.57 18.401c-13.017-3.574-25.073-7.775-36.326-12.659c19.438-16.93 41.97-30.393 66.628-39.41c-7.826 9.607-15.114 20.852-21.732 33.668m-17.892 43.84c-10.398 32.755-16.455 69.878-17.597 108.701h-82.09c3.041-48.266 22.254-92.208 52.281-126.457c14.553 7.039 30.243 12.923 47.406 17.756M125.973 269.5c1.142 38.814 7.196 75.928 17.589 108.678c-16.978 4.812-32.778 10.77-47.359 17.823c-30.049-34.255-49.278-78.215-52.32-126.501zm26.909 134.116a258 258 0 0 0 8.58 18.425c6.618 12.816 13.906 24.061 21.731 33.669c-24.647-9.014-47.171-22.469-66.604-39.389c11.31-4.92 23.428-9.151 36.293-12.705m206.215.051c12.792 3.547 24.916 7.797 36.26 12.702c-19.421 16.898-41.926 30.336-66.55 39.341c7.825-9.608 15.113-20.853 21.732-33.669c3.036-5.88 5.887-12.018 8.558-18.374m-16.954-31.825c-23.709-4.874-47.99-7.655-72.643-8.367V269.5h89.516c-1.144 36.815-6.95 71.803-16.873 102.342M269.5 242.5v-94.023a457 457 0 0 0 33.056-2.267c13.854-1.458 27.024-3.464 39.606-5.993c9.912 30.525 15.712 65.492 16.855 102.283zm146.193 153.618l.068-.141c-14.598-7.008-30.463-12.952-47.339-17.75c10.403-32.762 16.463-69.894 17.605-108.728h82.089c-3.045 48.343-22.315 92.347-52.423 126.619\"/>",
        "vb": "0 0 512 512",
        "strw": "0"
    },
    "logos:svg": {
        "svg": "<path d=\"M245.235 153.524c14.012-14.012 14.011-36.811 0-50.823c-6.787-6.788-15.812-10.525-25.411-10.525c-2.28 0-4.523.208-6.712.617c9.538-6.524 15.72-17.495 15.72-29.694c0-19.816-16.122-35.937-35.938-35.937c-12.223 0-23.213 6.205-29.733 15.776c2.157-11.377-1.226-23.537-9.87-32.18C146.506 3.97 137.48.232 127.882.232S109.258 3.97 102.47 10.758C93.826 19.4 90.443 31.56 92.6 42.938c-6.519-9.57-17.509-15.776-29.733-15.776c-19.815 0-35.936 16.12-35.936 35.937c0 12.2 6.18 23.17 15.718 29.694a36.5 36.5 0 0 0-6.711-.617c-9.6 0-18.624 3.738-25.411 10.526C3.738 109.489 0 118.514 0 128.112c0 9.6 3.738 18.624 10.526 25.412c6.787 6.787 15.812 10.526 25.41 10.526c2.28 0 4.523-.208 6.712-.618c-9.538 6.525-15.718 17.496-15.718 29.695c0 19.815 16.12 35.936 35.936 35.936c12.224 0 23.215-6.206 29.734-15.776c-2.157 11.378 1.226 23.538 9.87 32.18c6.787 6.788 15.812 10.526 25.41 10.526c9.6 0 18.625-3.738 25.412-10.526c8.643-8.643 12.026-20.803 9.869-32.18c6.52 9.57 17.51 15.776 29.733 15.776c19.816 0 35.937-16.12 35.937-35.936c0-12.2-6.18-23.17-15.719-29.695c2.189.41 4.433.618 6.712.618c9.599 0 18.624-3.739 25.411-10.526\"/><path fill=\"#FFB13B\" d=\"M234.391 113.538c-8.049-8.048-21.099-8.048-29.148 0h-42.184l29.829-29.828c11.383 0 20.61-9.228 20.61-20.611s-9.227-20.612-20.61-20.612c-11.384 0-20.611 9.229-20.611 20.612l-29.829 29.829V50.743c8.049-8.049 8.049-21.099 0-29.148c-8.05-8.05-21.1-8.05-29.149 0s-8.049 21.1 0 29.148v42.185l-29.828-29.83c0-11.382-9.228-20.61-20.611-20.61s-20.611 9.228-20.611 20.61c0 11.384 9.228 20.612 20.61 20.612l29.83 29.828H50.504c-8.05-8.049-21.1-8.048-29.15 0c-8.048 8.05-8.048 21.1 0 29.15c8.05 8.048 21.1 8.048 29.15 0h42.183L62.86 172.515c-11.383 0-20.611 9.227-20.611 20.61c0 11.384 9.228 20.612 20.61 20.612c11.384 0 20.612-9.228 20.612-20.611l29.828-29.829v42.184c-8.049 8.049-8.049 21.1 0 29.149c8.05 8.049 21.1 8.049 29.15 0c8.048-8.05 8.048-21.1 0-29.15v-42.183l29.828 29.829c0 11.383 9.227 20.61 20.61 20.61c11.384 0 20.612-9.227 20.612-20.61c0-11.384-9.228-20.611-20.611-20.611l-29.83-29.829h42.185c8.05 8.05 21.1 8.05 29.148 0c8.05-8.049 8.05-21.1 0-29.149\"/>",
        "vb": "0 0 256 256",
        "strw": "0"
    },
    "emojione-v1:cherries": {
        "svg": "<path fill=\"#047d3f\" d=\"M25.808 3.692c5.582 6.717 10.657 13.85 12.04 22.673c.562 3.608.063 7.177 1.734 10.554c1.621 3.263 3.522 7.04 6.713 8.977c2.206 1.339 4.02-2.222 1.84-3.542c-2.847-1.727-5.437-7.185-5.897-10.347c-.529-3.646-.472-7.176-1.425-10.791c-1.971-7.461-6.81-13.865-11.659-19.695c-1.626-1.958-4.987.194-3.342 2.171\"/><path fill=\"#9f1d2b\" d=\"M62.09 46.24c-.334 9.173-7.282 16.365-15.513 16.07c-8.236-.301-14.637-7.982-14.305-17.16c.338-9.179 7.04-9.85 15.28-9.551c8.232.3 14.872 1.453 14.538 10.638\"/><path fill=\"#7f1425\" d=\"M42.28 45.27c.26-7.104 4.34-9.106 9.986-9.51c-1.47-.181-3.037-.28-4.672-.338c-8.238-.299-14.94.367-15.28 9.545c-.33 9.176 6.069 16.859 14.305 17.16c1.776.065 3.488-.24 5.096-.813c-5.739-2.478-9.695-8.783-9.435-16.04\"/><path fill=\"#fff\" d=\"M54.37 37.886s7.632 1.354 5.953 10.319c0 0-2.693-9.947-5.953-10.319\"/><path fill=\"#00a551\" d=\"M25.329 3.573c2.402 6.981-2.502 13.679-4.758 20.05c-1.687 4.756-1.343 11.867 1.277 16.264c1.317 2.218 4.658.032 3.346-2.176c-3.396-5.703-.955-13.07 1.512-18.496c2.437-5.37 4.414-11.08 2.409-16.898c-.839-2.428-4.63-1.185-3.786 1.255\"/><path fill=\"#be202e\" d=\"M38.653 48.05c-2.01 10.508-11.272 17.565-20.7 15.765c-9.43-1.8-15.447-11.776-13.432-22.28c2-10.512 9.84-10.1 19.272-8.297c9.426 1.802 16.865 4.304 14.86 14.812\"/><path fill=\"#9b1c29\" d=\"M16.01 43.44c1.558-8.135 6.609-9.715 13.183-9.184c-1.661-.472-3.447-.857-5.32-1.217c-9.432-1.8-17.271-2.214-19.282 8.296c-2 10.503 4.01 20.48 13.442 22.28c2.037.388 4.056.344 6.01-.038c-6.168-3.862-9.611-11.824-8.03-20.14\"/><path fill=\"#fff\" d=\"M31.24 37.08s8.553 2.906 5.03 12.933c0 0-1.343-11.932-5.03-12.933\"/><path fill=\"#047d3f\" d=\"M27.519.084c-2.896 1.064-5.287 1.47-8.384 1.233c-2.089-.161-4.446-.062-6.301 1.032c-2.031 1.198-3.314 3.02-4.466 5.04c-.641 1.14-1.191 2.325-1.852 3.451c-.933 1.591-2.51 2.029-3.968 2.95c-.768.492-.726 1.717.103 2.126c4.392 2.138 8.696 4.744 13.327 1.766c2.168-1.386 3.606-3.869 4.999-5.957c1.981-2.981 4.252-5.452 6.619-8.109c.296-.336.356-.708.281-1.046c.137-.048.276-.1.416-.147c1.483-.545.719-2.889-.774-2.334m-8.05 6.882q.001-.047-.004-.093c.042-.022.082-.038.123-.06c-.036.056-.082.104-.119.153\"/>",
        "vb": "0 0 64 64",
        "strw": "0"
    },
    "flat-color-icons:data-recovery": {
        "svg": "<path fill=\"#D1C4E9\" d=\"M38 7H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2V9c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2\"/><g fill=\"#F44336\"><path d=\"M35 28h6v20h-6z\"/><path d=\"M28 35h20v6H28z\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:genealogy": {
        "svg": "<path fill=\"#CFD8DC\" d=\"M40 9V7h-9v5H15v11H8v2h7v11h16v5h9v-2h-7v-8h7v-2h-9v5H17V14h14v5h9v-2h-7V9z\"/><path fill=\"#00BCD4\" d=\"M4 20h8v8H4z\"/><path fill=\"#3F51B5\" d=\"M36 14h8v8h-8zm0-10h8v8h-8zM20 9h8v8h-8zm0 22h8v8h-8zm16 5h8v8h-8zm0-10h8v8h-8z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:search": {
        "svg": "<g fill=\"#616161\"><path d=\"m29.175 31.99l2.828-2.827l12.019 12.019l-2.828 2.827z\"/><circle cx=\"20\" cy=\"20\" r=\"16\"/></g><path fill=\"#37474F\" d=\"m32.45 35.34l2.827-2.828l8.696 8.696l-2.828 2.828z\"/><circle cx=\"20\" cy=\"20\" r=\"13\" fill=\"#64B5F6\"/><path fill=\"#BBDEFB\" d=\"M26.9 14.2c-1.7-2-4.2-3.2-6.9-3.2s-5.2 1.2-6.9 3.2c-.4.4-.3 1.1.1 1.4c.4.4 1.1.3 1.4-.1C16 13.9 17.9 13 20 13s4 .9 5.4 2.5c.2.2.5.4.8.4c.2 0 .5-.1.6-.2c.4-.4.4-1.1.1-1.5\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:stack-of-photos": {
        "svg": "<path fill=\"#64B5F6\" d=\"m17.474 8.578l26.544 8.904l-8.904 26.544L8.57 35.122z\"/><path fill=\"#1E88E5\" d=\"m19.238 12.504l20.922 6.82l-6.2 19.02l-20.922-6.82z\"/><path fill=\"#90CAF9\" d=\"m10.881 5.778l27.524 5.068l-5.068 27.524l-27.524-5.068z\"/><path fill=\"#42A5F5\" d=\"m13.219 9.444l21.67 3.85l-3.5 19.7l-21.67-3.85z\"/><path fill=\"#BBDEFB\" d=\"M4 4h28v28H4z\"/><path fill=\"#4CAF50\" d=\"M7 7h22v20H7z\"/><path fill=\"#fff\" d=\"M16 13c0-1.1.9-2 2-2s2 .9 2 2s-2 4-2 4s-2-2.9-2-4m4 8c0 1.1-.9 2-2 2s-2-.9-2-2s2-4 2-4s2 2.9 2 4\"/><path fill=\"#fff\" d=\"M13.5 16.7c-1-.6-1.3-1.8-.7-2.7c.6-1 1.8-1.3 2.7-.7c1 .6 2.5 3.7 2.5 3.7s-3.5.3-4.5-.3m9 .6c1 .6 1.3 1.8.7 2.7c-.6 1-1.8 1.3-2.7.7c-1-.5-2.5-3.7-2.5-3.7s3.5-.3 4.5.3\"/><path fill=\"#fff\" d=\"M22.5 16.7c1-.6 1.3-1.8.7-2.7c-.6-1-1.8-1.3-2.7-.7c-1 .5-2.5 3.7-2.5 3.7s3.5.3 4.5-.3m-9 .6c-1 .6-1.3 1.8-.7 2.7c.6 1 1.8 1.3 2.7.7c1-.6 2.5-3.7 2.5-3.7s-3.5-.3-4.5.3\"/><circle cx=\"18\" cy=\"17\" r=\"2\" fill=\"#FFC107\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:graduation-cap": {
        "svg": "<g fill=\"#37474F\"><path d=\"M9 20h30v13H9z\"/><ellipse cx=\"24\" cy=\"33\" rx=\"15\" ry=\"6\"/></g><path fill=\"#78909C\" d=\"M23.1 8.2L.6 18.1c-.8.4-.8 1.5 0 1.9l22.5 9.9q.9.3 1.8 0L47.4 20c.8-.4.8-1.5 0-1.9L24.9 8.2q-.9-.45-1.8 0\"/><g fill=\"#37474F\"><path d=\"m43.2 20.4l-20-3.4c-.5-.1-1.1.3-1.2.8s.3 1.1.8 1.2L42 22.2V37c0 .6.4 1 1 1s1-.4 1-1V21.4c0-.5-.4-.9-.8-1\"/><circle cx=\"43\" cy=\"37\" r=\"2\"/><path d=\"M46 40c0 1.7-3 6-3 6s-3-4.3-3-6s1.3-3 3-3s3 1.3 3 3\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fluent-emoji-flat:bookmark-tabs": {
        "svg": "<g fill=\"none\"><path fill=\"#B4ACBC\" d=\"M4.5 1A2.5 2.5 0 0 0 2 3.5v23A2.5 2.5 0 0 0 4.5 29H7v.5A1.5 1.5 0 0 0 8.5 31h17a1.5 1.5 0 0 0 1.5-1.5v-23A1.5 1.5 0 0 0 25.5 5h-4.586l-3.268-3.268A2.5 2.5 0 0 0 15.88 1z\"/><path fill=\"#F3EEF8\" d=\"M3 3.5A1.5 1.5 0 0 1 4.5 2h11.379a1.5 1.5 0 0 1 1.06.44l5.622 5.62A1.5 1.5 0 0 1 23 9.122V26.5a1.5 1.5 0 0 1-1.5 1.5h-17A1.5 1.5 0 0 1 3 26.5z\"/><path fill=\"#998EA4\" d=\"M6.5 11a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1zm0 3a.5.5 0 0 0 0 1h13a.5.5 0 0 0 0-1zM6 17.5a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 0 1h-13a.5.5 0 0 1-.5-.5m.5 2.5a.5.5 0 0 0 0 1h8a.5.5 0 0 0 0-1z\"/><path fill=\"#CDC4D6\" d=\"M16 2.005a1.5 1.5 0 0 1 .94.434l5.62 5.622a1.5 1.5 0 0 1 .435.939H17.5A1.5 1.5 0 0 1 16 7.5z\"/><path fill=\"#F70A8D\" d=\"M22.36 13.118a.5.5 0 0 1 .323-.118H25.5a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-2.817a.5.5 0 0 1-.322-.118l-1.187-1a.5.5 0 0 1 0-.764z\"/><path fill=\"#F9C23C\" d=\"M25.36 20.118a.5.5 0 0 1 .323-.118H28.5a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-2.817a.5.5 0 0 1-.322-.118l-1.187-1a.5.5 0 0 1 0-.764z\"/></g>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "flat-color-icons:conference-call": {
        "svg": "<circle cx=\"12\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/><path fill=\"#455A64\" d=\"M2 34.7s2.8-6.3 10-6.3s10 6.3 10 6.3V38H2zm44 0s-2.8-6.3-10-6.3s-10 6.3-10 6.3V38h20z\"/><circle cx=\"24\" cy=\"17\" r=\"6\" fill=\"#FFB74D\"/><path fill=\"#607D8B\" d=\"M36 34.1s-3.3-7.5-12-7.5s-12 7.5-12 7.5V38h24z\"/><circle cx=\"36\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/><circle cx=\"12\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/><circle cx=\"36\" cy=\"21\" r=\"5\" fill=\"#FFA726\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fluent-emoji-flat:heavy-dollar-sign": {
        "svg": "<path fill=\"#00D26A\" d=\"M22.788 20.56c-.04-2.6-1.91-6.46-6.89-6.46c-2.85 0-3.24-1.74-3.27-2.39c.03-.81.4-2.69 3.23-2.69c2.94 0 3.28 2.07 3.31 2.71c.03.99.88 1.84 1.85 1.75c1-.02 1.79-.85 1.76-1.85c-.05-2.17-1.49-5.24-5.1-6.03l.03-1.77c.01-1-.79-1.82-1.79-1.83h-.02c-.99 0-1.8.8-1.81 1.79l-.02 1.8c-3.64.77-5.04 3.88-5.06 6.11c.04 2.42 1.91 6.02 6.89 6.02c2.81 0 3.21 2.04 3.27 2.8c-.08.49-.54 2.28-3.27 2.28c-2.77 0-3.21-1.86-3.28-2.41c-.09-1-.95-1.72-1.97-1.64c-1 .09-1.73.97-1.64 1.96c.15 1.71 1.44 4.75 5.07 5.52v2.07c0 1 .81 1.81 1.81 1.81s1.82-.81 1.82-1.81v-2.07a6.28 6.28 0 0 0 5.07-5.48l.01-.1z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "twemoji:sports-medal": {
        "svg": "<path fill=\"#55ACEE\" d=\"m18 8l-7-8H0l14 17l11.521-4.75z\"/><path fill=\"#3B88C3\" d=\"m25 0l-7 8l5.39 7.312l1.227-1.489L36 0z\"/><path fill=\"#FFAC33\" d=\"M23.26 16.026c.08-.217.131-.448.131-.693a2 2 0 0 0-2-2h-6.667a2 2 0 0 0-2 2c0 .245.05.476.131.693c-3.258 1.826-5.464 5.307-5.464 9.307C7.391 31.224 12.166 36 18.058 36s10.667-4.776 10.667-10.667c-.001-4-2.206-7.481-5.465-9.307\"/><circle cx=\"18.058\" cy=\"25.333\" r=\"8\" fill=\"#FFD983\"/><path fill=\"#FFAC33\" d=\"M21.278 30.634a.7.7 0 0 1-.406-.131L18 28.444l-2.871 2.059a.697.697 0 0 1-1.071-.777l1.071-3.456l-2.845-2.005a.698.698 0 0 1 .409-1.259l3.524-.005l1.122-3.37a.697.697 0 0 1 1.324 0l1.103 3.37l3.542.005a.697.697 0 0 1 .409 1.259l-2.846 2.005l1.071 3.456a.695.695 0 0 1-.664.908\"/>",
        "vb": "0 0 36 36",
        "strw": "0"
    },
    "openmoji:boy-light-skin-tone": {
        "svg": "<path fill=\"#92D3F5\" d=\"M54 60.9V57c0-5-4.2-9-9.2-9q-9 7.5-18 0c-5 0-8.8 4-8.8 9v3.9z\"/><path fill=\"#a57939\" d=\"M24 36c-2 0-3-12 0-16c6-8 18-8 24 0c3 4 2 16 0 16\"/><path fill=\"#fadcbc\" d=\"M25 34c0 8 4.8 11.9 10.8 11.9S47 42 47 34s-3-10-3-10c-8 4-14 2-16 0c0 0-3 2-3 10\"/><path fill=\"none\" stroke=\"#000\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M24 36c-2 0-3-12 0-16c6-8 18-8 24 0c3 4 2 16 0 16m6 24v-3.1c0-5-4.2-9-9.2-9q-9 7.5-18 0c-5 0-8.8 4-8.8 9V60\"/><path d=\"M42 33c0 1.1-.9 2-2 2s-2-.9-2-2s.9-2 2-2s2 .9 2 2m-8 0c0 1.1-.9 2-2 2s-2-.9-2-2s.9-2 2-2s2 .9 2 2\"/><path fill=\"none\" stroke=\"#000\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M25 34c0 8 4.8 11.9 10.8 11.9S47 42 47 34s-3-10-3-10c-8 4-14 2-16 0c0 0-3 2-3 10z\"/><path fill=\"none\" stroke=\"#000\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M32.8 39.2c1.9 1 4.1 1 6 0\"/>",
        "vb": "0 0 72 72",
        "strw": "0"
    },
    "openmoji:girl-medium-light-skin-tone": {
        "svg": "<path fill=\"#92D3F5\" d=\"M18 60.9V57c0-5 3.8-9 8.8-9q9 7.5 18 0c5 0 9.2 4 9.2 9v3.9z\"/><path fill=\"#fcea2b\" d=\"M22.1 49.4c1.4-.9 3-1.4 4.7-1.4q9 7.5 18 0c1.7 0 3.3.5 4.7 1.3c1.2-.9 2.3-1.8 3.1-2.6c2.9-3.1 3.7-6.8 2-10.8c-2.4-5.7-3-5.6-3.4-12.8c0-4.2-3.2-7.7-7.4-8c0 0-2.6-3-8-3c-6.4 0-12.2 3.5-13.7 9c-1.5 5.6.3 7.8-1.3 12C19 37.8 15 41.5 19.5 47c.6.7 1.5 1.6 2.6 2.4\"/><path fill=\"#debb90\" d=\"M25 32c0 8 4.8 12.9 10.8 12.9S47 40 47 32c0-4-1-6-1-6l-4-4s-12 2-16 4c0 0-1 2-1 6\"/><path fill=\"none\" stroke=\"#000\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 60v-3.1c0-5 3.8-9 8.8-9q9 7.5 18 0c5 0 9.2 4 9.2 9V60\"/><path d=\"M42 31c0 1.1-.9 2-2 2s-2-.9-2-2s.9-2 2-2s2 .9 2 2m-8 0c0 1.1-.9 2-2 2s-2-.9-2-2s.9-2 2-2s2 .9 2 2\"/><path fill=\"none\" stroke=\"#000\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M25 32c0 8 4.8 12.9 10.8 12.9S47 40 47 32c0-4-1-6-1-6l-4-4s-12 2-16 4c0 0-1 2-1 6z\"/><path fill=\"none\" stroke=\"#000\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M32.8 38.2c1.9 1 4.1 1 6 0\"/><path fill=\"none\" stroke=\"#000\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 46c-4-6-1.8-8.3.1-13c1.6-4.2-.2-6.5 1.3-12s7.3-9 13.7-9c5.4 0 8 3 8 3c4.2.4 7.4 3.9 7.4 8c.4 7.2 1 7.2 3.4 12.8c1.8 4.1 1 6.8-1.9 10.1\"/>",
        "vb": "0 0 72 72",
        "strw": "0"
    },
    "flat-color-icons:about": {
        "svg": "<path fill=\"#2196F3\" d=\"M37 40H11l-6 6V12c0-3.3 2.7-6 6-6h26c3.3 0 6 2.7 6 6v22c0 3.3-2.7 6-6 6\"/><g fill=\"#fff\"><path d=\"M22 20h4v11h-4z\"/><circle cx=\"24\" cy=\"15\" r=\"2\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:support": {
        "svg": "<path fill=\"#607D8B\" d=\"M44.7 11L36 19.6s-2.6 0-5.2-2.6s-2.6-5.2-2.6-5.2l8.7-8.7c-4.9-1.2-10.8.4-14.4 4c-5.4 5.4-.6 12.3-2 13.7C12.9 28.7 5.1 34.7 4.9 35c-2.3 2.3-2.4 6-.2 8.2s5.9 2.1 8.2-.2c.3-.3 6.7-8.4 14.2-15.9c1.4-1.4 8 3.7 13.6-1.8c3.5-3.6 5.2-9.4 4-14.3M9.4 41.1c-1.4 0-2.5-1.1-2.5-2.5C6.9 37.1 8 36 9.4 36s2.5 1.1 2.5 2.5s-1.1 2.6-2.5 2.6\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:gallery": {
        "svg": "<path fill=\"#E65100\" d=\"M41 42H13c-2.2 0-4-1.8-4-4V18c0-2.2 1.8-4 4-4h28c2.2 0 4 1.8 4 4v20c0 2.2-1.8 4-4 4\"/><path fill=\"#F57C00\" d=\"M35 36H7c-2.2 0-4-1.8-4-4V12c0-2.2 1.8-4 4-4h28c2.2 0 4 1.8 4 4v20c0 2.2-1.8 4-4 4\"/><circle cx=\"30\" cy=\"16\" r=\"3\" fill=\"#FFF9C4\"/><path fill=\"#942A09\" d=\"M17 17.9L8 31h18z\"/><path fill=\"#BF360C\" d=\"M28 23.5L22 31h12z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:edit-image": {
        "svg": "<path fill=\"#8CBCD6\" d=\"M31 41H8c-2.2 0-4-1.8-4-4V11c0-2.2 1.8-4 4-4h32c2.2 0 4 1.8 4 4v17c0 7.2-5.8 13-13 13\"/><circle cx=\"35\" cy=\"16\" r=\"3\" fill=\"#B3DDF5\"/><path fill=\"#9AC9E3\" d=\"M20 16L9 32h22z\"/><path fill=\"#B3DDF5\" d=\"m31 22l-8 10h16z\"/><path fill=\"#E57373\" d=\"m47.7 29.1l-2.8-2.8c-.4-.4-1.1-.4-1.6 0L42 27.6l4.4 4.4l1.3-1.3c.4-.4.4-1.1 0-1.6\"/><path fill=\"#FF9800\" d=\"M27.467 42.167L39.77 29.865l4.384 4.384L31.85 46.55z\"/><path fill=\"#B0BEC5\" d=\"m46.4 32.038l-2.192 2.192l-4.383-4.384l2.192-2.191z\"/><path fill=\"#FFC107\" d=\"M27.5 42.2L26 48l5.8-1.5z\"/><path fill=\"#37474F\" d=\"m26.7 45l-.7 3l3-.7z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:share": {
        "svg": "<path fill=\"#1976D2\" d=\"M38.1 31.2L19.4 24l18.7-7.2c1.5-.6 2.3-2.3 1.7-3.9c-.6-1.5-2.3-2.3-3.9-1.7l-26 10C8.8 21.6 8 22.8 8 24s.8 2.4 1.9 2.8l26 10c.4.1.7.2 1.1.2c1.2 0 2.3-.7 2.8-1.9c.6-1.6-.2-3.3-1.7-3.9\"/><g fill=\"#1E88E5\"><circle cx=\"11\" cy=\"24\" r=\"7\"/><circle cx=\"37\" cy=\"14\" r=\"7\"/><circle cx=\"37\" cy=\"34\" r=\"7\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fluent-color:code-block-24": {
        "svg": "<g fill=\"none\"><path fill=\"url(#fluentColorCodeBlock240)\" d=\"M3 6.25A3.25 3.25 0 0 1 6.25 3h11.5A3.25 3.25 0 0 1 21 6.25v11.5A3.25 3.25 0 0 1 17.75 21H6.25A3.25 3.25 0 0 1 3 17.75z\"/><path fill=\"url(#fluentColorCodeBlock241)\" d=\"M10.53 9.28L7.81 12l2.72 2.72a.75.75 0 1 1-1.06 1.06l-3.25-3.25a.75.75 0 0 1 0-1.06l3.25-3.25a.75.75 0 1 1 1.06 1.06m4-1.06l3.25 3.25a.75.75 0 0 1 0 1.06l-3.25 3.25a.75.75 0 1 1-1.06-1.06L16.19 12l-2.72-2.72a.75.75 0 0 1 1.06-1.06\"/><defs><linearGradient id=\"fluentColorCodeBlock240\" x1=\"7.389\" x2=\"17.26\" y1=\"3\" y2=\"21\" gradientUnits=\"userSpaceOnUse\"><stop offset=\".028\" stop-color=\"#E67EEA\"/><stop offset=\".438\" stop-color=\"#AD64D7\"/><stop offset=\"1\" stop-color=\"#794DC5\"/></linearGradient><linearGradient id=\"fluentColorCodeBlock241\" x1=\"8.845\" x2=\"13.544\" y1=\"8.308\" y2=\"20.472\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#FDFDFD\"/><stop offset=\"1\" stop-color=\"#F9DCFA\"/></linearGradient></defs></g>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "flat-color-icons:database": {
        "svg": "<path fill=\"#D1C4E9\" d=\"M38 7H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2V9c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "vscode-icons:file-type-light-json": {
        "svg": "<path fill=\"#fbc02d\" d=\"M4.014 14.976a2.5 2.5 0 0 0 1.567-.518a2.38 2.38 0 0 0 .805-1.358a15.3 15.3 0 0 0 .214-2.944q.012-2.085.075-2.747a5.2 5.2 0 0 1 .418-1.686a3 3 0 0 1 .755-1.018A3.05 3.05 0 0 1 9 4.125A6.8 6.8 0 0 1 10.544 4h.7v1.96h-.387a2.34 2.34 0 0 0-1.723.468a3.4 3.4 0 0 0-.425 2.092a36 36 0 0 1-.137 4.133a4.7 4.7 0 0 1-.768 2.06A4.6 4.6 0 0 1 6.1 16a3.8 3.8 0 0 1 1.992 1.754a8.9 8.9 0 0 1 .618 3.865q0 2.435.05 2.9a1.76 1.76 0 0 0 .504 1.181a2.64 2.64 0 0 0 1.592.337h.387V28h-.7a5.7 5.7 0 0 1-1.773-.2a2.97 2.97 0 0 1-1.324-.93a3.35 3.35 0 0 1-.681-1.63a24 24 0 0 1-.165-3.234a16.5 16.5 0 0 0-.214-3.106a2.4 2.4 0 0 0-.805-1.361a2.5 2.5 0 0 0-1.567-.524Zm23.972 2.035a2.5 2.5 0 0 0-1.567.524a2.4 2.4 0 0 0-.805 1.361a16.5 16.5 0 0 0-.212 3.109a24 24 0 0 1-.169 3.234a3.35 3.35 0 0 1-.681 1.63a2.97 2.97 0 0 1-1.324.93a5.7 5.7 0 0 1-1.773.2h-.7V26.04h.387a2.64 2.64 0 0 0 1.592-.337a1.76 1.76 0 0 0 .506-1.186q.05-.462.05-2.9a8.9 8.9 0 0 1 .618-3.865A3.8 3.8 0 0 1 25.9 16a4.6 4.6 0 0 1-1.7-1.286a4.7 4.7 0 0 1-.768-2.06a36 36 0 0 1-.137-4.133a3.4 3.4 0 0 0-.425-2.092a2.34 2.34 0 0 0-1.723-.468h-.387V4h.7a6.8 6.8 0 0 1 1.54.125a3.05 3.05 0 0 1 1.149.581a3 3 0 0 1 .755 1.018a5.2 5.2 0 0 1 .418 1.686q.062.662.075 2.747a15.3 15.3 0 0 0 .212 2.947a2.38 2.38 0 0 0 .805 1.355a2.5 2.5 0 0 0 1.567.518Z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "flat-color-icons:accept-database": {
        "svg": "<path fill=\"#D1C4E9\" d=\"M38 7H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2V9c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2m0 12H10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2h28c1.1 0 2-.9 2-2v-6c0-1.1-.9-2-2-2\"/><circle cx=\"38\" cy=\"38\" r=\"10\" fill=\"#43A047\"/><path fill=\"#DCEDC8\" d=\"M42.5 33.3L36.8 39l-2.7-2.7l-2.1 2.2l4.8 4.8l7.8-7.8z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:settings": {
        "svg": "<path fill=\"#607D8B\" d=\"M39.6 27.2c.1-.7.2-1.4.2-2.2s-.1-1.5-.2-2.2l4.5-3.2c.4-.3.6-.9.3-1.4L40 10.8c-.3-.5-.8-.7-1.3-.4l-5 2.3c-1.2-.9-2.4-1.6-3.8-2.2L29.4 5c-.1-.5-.5-.9-1-.9h-8.6c-.5 0-1 .4-1 .9l-.5 5.5c-1.4.6-2.7 1.3-3.8 2.2l-5-2.3c-.5-.2-1.1 0-1.3.4l-4.3 7.4c-.3.5-.1 1.1.3 1.4l4.5 3.2c-.1.7-.2 1.4-.2 2.2s.1 1.5.2 2.2L4 30.4c-.4.3-.6.9-.3 1.4L8 39.2c.3.5.8.7 1.3.4l5-2.3c1.2.9 2.4 1.6 3.8 2.2l.5 5.5c.1.5.5.9 1 .9h8.6c.5 0 1-.4 1-.9l.5-5.5c1.4-.6 2.7-1.3 3.8-2.2l5 2.3c.5.2 1.1 0 1.3-.4l4.3-7.4c.3-.5.1-1.1-.3-1.4zM24 35c-5.5 0-10-4.5-10-10s4.5-10 10-10s10 4.5 10 10s-4.5 10-10 10\"/><path fill=\"#455A64\" d=\"M24 13c-6.6 0-12 5.4-12 12s5.4 12 12 12s12-5.4 12-12s-5.4-12-12-12m0 17c-2.8 0-5-2.2-5-5s2.2-5 5-5s5 2.2 5 5s-2.2 5-5 5\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "lsicon:move-filled": {
        "svg": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m8 1.293l2.229 2.228l-.708.708L8.5 3.207V7.5h4.293l-1.021-1.021l.707-.708L14.707 8l-2.228 2.229l-.707-.708l1.02-1.021H8.5v4.293l1.021-1.021l.708.707L8 14.707L5.771 12.48l.708-.707l1.021 1.02V8.5H3.207L4.23 9.521l-.708.708L1.293 8L3.52 5.771l.708.708L3.207 7.5H7.5V3.207L6.479 4.23l-.709-.71z\" clip-rule=\"evenodd\"/>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "fluent-color:code-block-16": {
        "svg": "<g fill=\"none\"><path fill=\"url(#fluentColorCodeBlock160)\" d=\"M2 4.5A2.5 2.5 0 0 1 4.5 2h7A2.5 2.5 0 0 1 14 4.5v7a2.5 2.5 0 0 1-2.5 2.5h-7A2.5 2.5 0 0 1 2 11.5z\"/><path fill=\"url(#fluentColorCodeBlock161)\" d=\"M9.854 5.646a.5.5 0 1 0-.708.708L10.793 8L9.146 9.646a.5.5 0 0 0 .708.708l2-2a.5.5 0 0 0 0-.708zm-3 .708a.5.5 0 0 0-.708-.707l-2 2a.5.5 0 0 0 0 .707l2 2a.5.5 0 0 0 .708-.708L5.207 8z\"/><defs><linearGradient id=\"fluentColorCodeBlock160\" x1=\"4.975\" x2=\"11.665\" y1=\"2\" y2=\"14.2\" gradientUnits=\"userSpaceOnUse\"><stop offset=\".028\" stop-color=\"#E67EEA\"/><stop offset=\".438\" stop-color=\"#AD64D7\"/><stop offset=\"1\" stop-color=\"#794DC5\"/></linearGradient><linearGradient id=\"fluentColorCodeBlock161\" x1=\"5.897\" x2=\"8.694\" y1=\"5.692\" y2=\"13.416\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#FDFDFD\"/><stop offset=\"1\" stop-color=\"#F9DCFA\"/></linearGradient></defs></g>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "devicon-original:apachespark": {
        "svg": "<path fill=\"#e15919\" d=\"M57.646.004c-2.269.064-4.51 1.143-6.384 3.223c-1.03 1.146-1.905 2.51-2.63 3.855c-1.89 3.54-2.262 7.547-2.962 11.43l-3.852 21.81c-.148.856-.532 1.21-1.3 1.455l-28.268 8.98c-2.06.673-4.125 1.543-5.947 2.7c-5.558 3.53-6.38 9.338-2.207 14.438c1.842 2.256 4.216 3.843 6.85 4.996l17.603 7.843c.147.08.304.132.463.162l3.717 2.682s-3.7 40.948 3.246 43.781c-.061-.01-.41-.082-.41-.082s.704.761 2.603.537c1.454.27 1.262.226.074-.01c2.583-.334 7.337-2.497 15.578-10.784a47.396 47.396 0 0 0 1.776-1.676l17.8-19.217l4.163 1.465c.15.207.367.34.714.443l19.823 6.031c2.709.836 5.389 1.448 8.277 1.026c5.156-.755 8.951-5 8.9-10.192c-.02-2.28-.82-4.339-1.87-6.324l-13.128-24.898c-.418-.787-.405-1.296.196-2l22.054-25.922c1.428-1.703 2.717-3.529 3.465-5.645c1.643-4.67-.482-8.382-5.33-9.289c-2.229-.398-4.427-.188-6.6.385l-31.597 8.395c-.93.25-1.39.075-1.895-.772l-12.9-21.434c-.975-1.615-2.14-3.194-3.477-4.527C62.212.89 59.915-.059 57.646.004zm.944 19.736c.51.358.768.727 1.01 1.125l13.88 23.13c.382.628.725.85 1.485.648l24.443-6.497l5.885-1.54c-.087.493-.302.79-.537 1.068l-20.16 23.672c-.57.688-.623 1.17-.194 1.976l12.743 24.16l.585 1.237l-.015.02l-22.727-6.264l-.006-.018l-4.298-1.205l-25.493 28.256l4.663-37.15l-4.184-1.82l.008-.007l-23.674-9.4c.454-.413.86-.585 1.285-.717l28.777-9.096c.676-.21 1.061-.47 1.125-1.242l.403-2.355l3.875-21.807l1.12-6.174z\"/>",
        "vb": "0 0 128 128",
        "strw": "0"
    },
    "flat-color-icons:process": {
        "svg": "<g fill=\"#9C27B0\"><path d=\"m31 8l11.9 1.6l-9.8 9.8zM17 40L5.1 38.4l9.8-9.8zM8 17L9.6 5.1l9.8 9.8z\"/><path d=\"m9.3 21.2l-4.2.8c-.1.7-.1 1.3-.1 2c0 4.6 1.6 9 4.6 12.4l3-2.6C10.3 31.1 9 27.6 9 24c0-.9.1-1.9.3-2.8M24 5c-5.4 0-10.2 2.3-13.7 5.9l2.8 2.8C15.9 10.8 19.7 9 24 9c.9 0 1.9.1 2.8.3l.7-3.9C26.4 5.1 25.2 5 24 5m14.7 21.8l4.2-.8c.1-.7.1-1.3.1-2c0-4.4-1.5-8.7-4.3-12.1l-3.1 2.5c2.2 2.7 3.4 6.1 3.4 9.5c0 1-.1 2-.3 2.9m-3.8 7.5C32.1 37.2 28.3 39 24 39c-.9 0-1.9-.1-2.8-.3l-.7 3.9c1.2.2 2.4.3 3.5.3c5.4 0 10.2-2.3 13.7-5.9z\"/><path d=\"m40 31l-1.6 11.9l-9.8-9.8z\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:kindle": {
        "svg": "<path fill=\"#37474F\" d=\"M8 41V7c0-2.2 1.8-4 4-4h24c2.2 0 4 1.8 4 4v34c0 2.2-1.8 4-4 4H12c-2.2 0-4-1.8-4-4\"/><path fill=\"#eee\" d=\"M35 6H13c-.6 0-1 .4-1 1v29c0 .6.4 1 1 1h22c.6 0 1-.4 1-1V7c0-.6-.4-1-1-1\"/><path fill=\"#546E7A\" d=\"M20 40h8v2h-8z\"/><path fill=\"#A1A1A1\" d=\"M16 11h16v3H16zm0 7h16v2H16zm0 4h12v2H16zm0 4h16v2H16zm0 4h12v2H16z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:view-details": {
        "svg": "<path fill=\"#BBDEFB\" d=\"M7 4h34v40H7z\"/><path fill=\"#2196F3\" d=\"M13 26h4v4h-4zm0-8h4v4h-4zm0 16h4v4h-4zm0-24h4v4h-4zm8 16h14v4H21zm0-8h14v4H21zm0 16h14v4H21zm0-24h14v4H21z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "lsicon:move-up-filled": {
        "svg": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"m7.979 2l3.875 3.875l-.707.707l-2.668-2.668V13H14v1H2v-1h5.479V3.914L4.81 6.582l-.707-.707zM6.5 10H2V9h4.5zm7.5 0H9.5V9H14z\" clip-rule=\"evenodd\"/>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "lsicon:move-down-filled": {
        "svg": "<path fill=\"currentColor\" fill-rule=\"evenodd\" d=\"M7.479 3H2V2h12v1H8.479v9.086l2.668-2.668l.707.707L7.979 14l-3.875-3.875l.707-.707l2.668 2.668zM6.5 7H2V6h4.5zM14 7H9.5V6H14z\" clip-rule=\"evenodd\"/>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "akar-icons:align-to-top": {
        "svg": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 22V7m-7 7l7-7l7 7M3 2h18\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "akar-icons:align-to-bottom": {
        "svg": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 22h18M12 2v15m-7-7l7 7l7-7\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "material-symbols-light:toolbar-outline": {
        "svg": "<path fill=\"currentColor\" d=\"M5.616 20q-.691 0-1.153-.462T4 18.384V5.616q0-.691.463-1.153T5.616 4h12.769q.69 0 1.153.463T20 5.616v12.769q0 .69-.462 1.153T18.384 20zM5 8h14V5.616q0-.231-.192-.424T18.384 5H5.616q-.231 0-.424.192T5 5.616zm14 1H5v9.385q0 .23.192.423t.423.192h12.77q.23 0 .423-.192t.192-.423zM5 8v1zm0 0V5zm0 1v10z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "material-symbols-light:password-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M3 17.5h18q.213 0 .356.144t.144.357t-.144.356T21 18.5H3q-.213 0-.356-.144t-.144-.357t.144-.356T3 17.5m1-6.666l-.744 1.312q-.089.163-.265.207q-.178.045-.34-.044q-.161-.09-.205-.267q-.044-.179.048-.338l.744-1.312H1.75q-.192 0-.317-.125t-.125-.316t.125-.317t.317-.126h1.489l-.745-1.262q-.092-.16-.048-.338t.206-.267t.339-.044t.265.207L4 9.065l.744-1.261q.089-.163.266-.208t.338.045t.206.267t-.048.338L4.76 9.508h1.49q.192 0 .317.125t.125.316t-.125.317t-.317.126H4.761l.745 1.312q.092.16.048.338t-.206.267q-.161.089-.338.044q-.177-.044-.266-.207zm8 0l-.744 1.312q-.089.163-.266.207q-.177.045-.338-.044q-.162-.09-.206-.267q-.044-.179.048-.338l.744-1.312H9.75q-.192 0-.317-.125t-.125-.316t.125-.317t.317-.126h1.489l-.745-1.262q-.092-.16-.048-.338t.206-.267t.339-.044t.265.207L12 9.065l.744-1.261q.089-.163.266-.208t.338.045q.162.09.206.267t-.048.338l-.744 1.262h1.488q.192 0 .317.125t.125.316t-.125.317t-.317.126h-1.489l.745 1.312q.092.16.048.338t-.206.267t-.339.044t-.265-.207zm8 0l-.744 1.312q-.089.163-.266.207q-.177.045-.338-.044q-.162-.09-.206-.267q-.044-.179.048-.338l.744-1.312H17.75q-.192 0-.317-.125t-.125-.316t.125-.317t.317-.126h1.489l-.745-1.262q-.092-.16-.048-.338t.206-.267t.338-.044t.266.207L20 9.065l.744-1.261q.089-.163.266-.208t.338.045t.206.267t-.048.338l-.744 1.262h1.488q.192 0 .317.125t.125.316t-.125.317t-.317.126h-1.489l.745 1.312q.092.16.048.338t-.206.267t-.338.044t-.266-.207z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "material-symbols-light:border-outer-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M12 8.923q-.261 0-.438-.177t-.177-.438t.177-.439t.438-.177t.439.177t.176.439t-.177.438t-.438.177m-3.692 3.693q-.262 0-.439-.177T7.692 12t.177-.438t.439-.177t.438.177t.177.438t-.177.439q-.177.176-.438.176m3.692 0q-.261 0-.438-.177T11.385 12t.177-.438t.438-.177t.439.177t.176.438t-.177.439t-.438.176m3.692 0q-.261 0-.438-.177T15.077 12t.177-.438t.438-.177t.439.177t.177.438t-.177.439t-.439.176M12 16.308q-.261 0-.438-.177t-.177-.439t.177-.438t.438-.177t.439.177t.176.438t-.177.439t-.438.177M5.616 19h12.769q.269 0 .442-.173t.173-.442V5.615q0-.269-.173-.442T18.385 5H5.615q-.269 0-.442.173T5 5.616v12.769q0 .269.173.442t.443.173m0 1q-.667 0-1.141-.475T4 18.386V5.615q0-.666.475-1.14T5.615 4h12.77q.666 0 1.14.475T20 5.615v12.77q0 .666-.475 1.14t-1.14.475z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "material-symbols-light:border-clear-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M12 8.923q-.261 0-.438-.177t-.177-.438t.177-.439t.438-.177t.439.177t.176.439t-.177.438t-.438.177m-3.692 3.693q-.262 0-.439-.177T7.692 12t.177-.438t.439-.177t.438.177t.177.438t-.177.439q-.177.176-.438.176m3.692 0q-.261 0-.438-.177T11.385 12t.177-.438t.438-.177t.439.177t.176.438t-.177.439t-.438.176m3.692 0q-.261 0-.438-.177T15.077 12t.177-.438t.438-.177t.439.177t.177.438t-.177.439t-.439.176M12 16.308q-.261 0-.438-.177t-.177-.439t.177-.438t.438-.177t.439.177t.176.438t-.177.439t-.438.177M4.616 5.23q-.262 0-.439-.177T4 4.616t.177-.439T4.616 4t.438.177t.177.439t-.177.438t-.439.177m3.693 0q-.262 0-.439-.177t-.177-.438t.177-.439T8.308 4t.438.177t.177.439t-.177.438t-.438.177m3.692 0q-.261 0-.438-.177t-.177-.438t.177-.439T12 4t.439.177t.176.439t-.177.438T12 5.23m3.692 0q-.261 0-.438-.177t-.177-.439t.177-.438t.438-.177t.439.177t.177.439t-.177.438t-.439.177m3.692 0q-.261 0-.438-.177t-.177-.439t.177-.438t.438-.177t.439.177t.177.439t-.177.438t-.438.177M4.615 8.923q-.261 0-.438-.177T4 8.308t.177-.439t.439-.177t.438.177t.177.439t-.177.438t-.438.177m14.769 0q-.262 0-.439-.177t-.177-.438t.177-.439t.439-.177t.438.177q.177.177.177.439t-.177.438t-.438.177m-14.77 3.693q-.261 0-.438-.177Q4 12.26 4 12t.177-.438t.439-.177t.438.177t.177.438t-.177.439t-.439.176m14.77 0q-.262 0-.439-.177T18.77 12t.177-.438t.438-.177t.439.177T20 12t-.177.439t-.438.176m-14.77 3.693q-.261 0-.438-.177T4 15.692t.177-.438t.439-.177t.438.177t.177.438t-.177.439t-.438.177m14.769 0q-.262 0-.439-.177t-.177-.439t.177-.438t.439-.177t.438.177q.177.177.177.438t-.177.439t-.438.177M4.615 20q-.261 0-.438-.177T4 19.385t.177-.439t.439-.177q.261 0 .438.177t.177.438t-.177.439t-.438.177m3.692 0q-.262 0-.439-.177t-.177-.438t.177-.439t.439-.177t.438.177t.177.438t-.177.439t-.438.177M12 20q-.261 0-.438-.177t-.177-.438t.177-.439t.438-.177t.439.177t.176.438q0 .262-.177.439T12 20m3.692 0q-.261 0-.438-.177t-.177-.438t.177-.439t.438-.177t.439.177t.177.438t-.177.439t-.439.177m3.692 0q-.261 0-.438-.177t-.177-.438t.177-.439t.438-.177t.439.177t.177.438q0 .262-.177.439t-.438.177\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "mynaui:refresh-alt": {
        "svg": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"><path d=\"M3.5 8c1.392-3.179 4.823-5 8.522-5c4.679 0 8.525 3.552 8.978 8.1\"/><path d=\"M7.511 8.4h-3.97a.54.54 0 0 1-.54-.54V3.9M20.5 16c-1.392 3.179-4.823 5-8.522 5C7.299 21 3.453 17.448 3 12.9\"/><path d=\"M16.489 15.6h3.97a.54.54 0 0 1 .541.54v3.96\"/></g>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "devicon-original:gitbook": {
        "svg": "<path fill=\"#346ddb\" fill-rule=\"evenodd\" d=\"M67.008 20.596a10.275 10.275 0 0 0-4.677 1.186L8.385 50.25a11.991 11.991 0 0 0-3.746 3.09A11.982 11.982 0 0 0 2 60.84v3.248a15.437 15.437 0 0 0 .596 4.244a15.382 15.382 0 0 0 2.93 5.56a15.379 15.379 0 0 0 4.983 3.964l33.521 16.76a11.196 11.196 0 0 0 .749 5.999a11.06 11.06 0 0 0 1.665 2.744a11.183 11.183 0 0 0 4.251 3.172a11.03 11.03 0 0 0 4.328.873a11.32 11.32 0 0 0 2.24-.225a11.022 11.022 0 0 0 3.975-1.673a11.166 11.166 0 0 0 4.028-4.891a11.03 11.03 0 0 0 .816-3.191a11.252 11.252 0 0 0-.106-3.052l41.18-21.734a11.185 11.185 0 0 0 2.532 1.839a11.076 11.076 0 0 0 6.331 1.228a11.112 11.112 0 0 0 3.19-.816a11.06 11.06 0 0 0 2.745-1.665a11.186 11.186 0 0 0 3.172-4.251a11.027 11.027 0 0 0 .817-3.191a11.238 11.238 0 0 0-.443-4.443a11.053 11.053 0 0 0-2.039-3.765a11.188 11.188 0 0 0-4.251-3.172a11.032 11.032 0 0 0-4.327-.874a11.29 11.29 0 0 0-2.241.226a11.045 11.045 0 0 0-4.831 2.313a11.186 11.186 0 0 0-3.172 4.251a11.028 11.028 0 0 0-.874 4.327a11.187 11.187 0 0 0 .172 1.957L62.779 88.324a11.193 11.193 0 0 0-2.54-1.856a11.062 11.062 0 0 0-5.217-1.298a11.249 11.249 0 0 0-2.266.231a11.032 11.032 0 0 0-4.013 1.71a11.172 11.172 0 0 0-1.653 1.387L14.048 71.977a9.427 9.427 0 0 1-4.842-5.828a9.402 9.402 0 0 1-.365-2.597a6.414 6.414 0 0 1 .201-1.589a6.093 6.093 0 0 1 1.432-2.613a6.254 6.254 0 0 1 1.745-1.325a6.294 6.294 0 0 1 2.104-.647a6.109 6.109 0 0 1 3.023.403L50.305 74.26a10.269 10.269 0 0 0 9.38-.103l52.493-27.705c.231-.122.433-.27.605-.437a2.496 2.496 0 0 0 .683-1.189a2.616 2.616 0 0 0-.282-1.967a2.493 2.493 0 0 0-.441-.546a2.612 2.612 0 0 0-.615-.425L71.711 21.68a10.271 10.271 0 0 0-4.702-1.082zm47.875 39.774a4.286 4.286 0 0 1 1.664.336a4.268 4.268 0 0 1 1.882 1.55a4.264 4.264 0 0 1 .643 3.252a4.242 4.242 0 0 1-.643 1.529a4.298 4.298 0 0 1-1.882 1.549a4.24 4.24 0 0 1-1.664.336a4.308 4.308 0 0 1-1.664-.336a4.267 4.267 0 0 1-1.882-1.55a4.269 4.269 0 0 1 0-4.78a4.298 4.298 0 0 1 2.684-1.8c.279-.056.567-.086.862-.086zM55.024 92.013a4.287 4.287 0 0 1 1.664.336a4.267 4.267 0 0 1 1.882 1.55a4.265 4.265 0 0 1 .394 4.054a4.268 4.268 0 0 1-1.55 1.881a4.266 4.266 0 0 1-5.413-.522a4.297 4.297 0 0 1-.917-1.359a4.239 4.239 0 0 1-.336-1.664a4.286 4.286 0 0 1 .336-1.665a4.267 4.267 0 0 1 .917-1.359a4.299 4.299 0 0 1 1.359-.916a4.244 4.244 0 0 1 1.664-.336z\"/>",
        "vb": "0 0 128 128",
        "strw": "0"
    },
    "devicon:vscode": {
        "svg": "<mask id=\"deviconVscode0\" width=\"128\" height=\"128\" x=\"0\" y=\"0\" maskUnits=\"userSpaceOnUse\" style=\"mask-type:alpha\"><path fill=\"#fff\" fill-rule=\"evenodd\" d=\"M90.767 127.126a7.97 7.97 0 0 0 6.35-.244l26.353-12.681a8 8 0 0 0 4.53-7.209V21.009a8 8 0 0 0-4.53-7.21L97.117 1.12a7.97 7.97 0 0 0-9.093 1.548l-50.45 46.026L15.6 32.013a5.33 5.33 0 0 0-6.807.302l-7.048 6.411a5.335 5.335 0 0 0-.006 7.888L20.796 64L1.74 81.387a5.336 5.336 0 0 0 .006 7.887l7.048 6.411a5.33 5.33 0 0 0 6.807.303l21.974-16.68l50.45 46.025a8 8 0 0 0 2.743 1.793Zm5.252-92.183L57.74 64l38.28 29.058V34.943Z\" clip-rule=\"evenodd\"/></mask><g mask=\"url(#deviconVscode0)\"><path fill=\"#0065A9\" d=\"M123.471 13.82L97.097 1.12A7.97 7.97 0 0 0 88 2.668L1.662 81.387a5.333 5.333 0 0 0 .006 7.887l7.052 6.411a5.33 5.33 0 0 0 6.811.303l103.971-78.875c3.488-2.646 8.498-.158 8.498 4.22v-.306a8 8 0 0 0-4.529-7.208Z\"/><g filter=\"url(#deviconVscode1)\"><path fill=\"#007ACC\" d=\"m123.471 114.181l-26.374 12.698A7.97 7.97 0 0 1 88 125.333L1.662 46.613a5.333 5.333 0 0 1 .006-7.887l7.052-6.411a5.33 5.33 0 0 1 6.811-.303l103.971 78.874c3.488 2.647 8.498.159 8.498-4.219v.306a8 8 0 0 1-4.529 7.208\"/></g><g filter=\"url(#deviconVscode2)\"><path fill=\"#1F9CF0\" d=\"M97.098 126.882A7.98 7.98 0 0 1 88 125.333c2.952 2.952 8 .861 8-3.314V5.98c0-4.175-5.048-6.266-8-3.313a7.98 7.98 0 0 1 9.098-1.549L123.467 13.8A8 8 0 0 1 128 21.01v85.982a8 8 0 0 1-4.533 7.21z\"/></g><path fill=\"url(#deviconVscode3)\" fill-rule=\"evenodd\" d=\"M90.69 127.126a7.97 7.97 0 0 0 6.349-.244l26.353-12.681a8 8 0 0 0 4.53-7.21V21.009a8 8 0 0 0-4.53-7.21L97.039 1.12a7.97 7.97 0 0 0-9.093 1.548l-50.45 46.026l-21.974-16.68a5.33 5.33 0 0 0-6.807.302l-7.048 6.411a5.336 5.336 0 0 0-.006 7.888L20.718 64L1.662 81.386a5.335 5.335 0 0 0 .006 7.888l7.048 6.411a5.33 5.33 0 0 0 6.807.303l21.975-16.681l50.45 46.026a8 8 0 0 0 2.742 1.793m5.252-92.184L57.662 64l38.28 29.057z\" clip-rule=\"evenodd\" opacity=\".25\"/></g><defs><filter id=\"deviconVscode1\" width=\"144.744\" height=\"113.408\" x=\"-8.411\" y=\"22.594\" color-interpolation-filters=\"sRGB\" filterUnits=\"userSpaceOnUse\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" result=\"hardAlpha\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"/><feOffset/><feGaussianBlur stdDeviation=\"4.167\"/><feColorMatrix values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0\"/><feBlend in2=\"BackgroundImageFix\" mode=\"overlay\" result=\"effect1_dropShadow_1_36\"/><feBlend in=\"SourceGraphic\" in2=\"effect1_dropShadow_1_36\" result=\"shape\"/></filter><filter id=\"deviconVscode2\" width=\"56.667\" height=\"144.007\" x=\"79.667\" y=\"-8.004\" color-interpolation-filters=\"sRGB\" filterUnits=\"userSpaceOnUse\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" result=\"hardAlpha\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"/><feOffset/><feGaussianBlur stdDeviation=\"4.167\"/><feColorMatrix values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0\"/><feBlend in2=\"BackgroundImageFix\" mode=\"overlay\" result=\"effect1_dropShadow_1_36\"/><feBlend in=\"SourceGraphic\" in2=\"effect1_dropShadow_1_36\" result=\"shape\"/></filter><linearGradient id=\"deviconVscode3\" x1=\"63.922\" x2=\"63.922\" y1=\".33\" y2=\"127.67\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#fff\"/><stop offset=\"1\" stop-color=\"#fff\" stop-opacity=\"0\"/></linearGradient></defs>",
        "vb": "0 0 128 128",
        "strw": "0"
    },
    "devicon:reactnative": {
        "svg": "<path fill=\"#61dafb\" d=\"M37.965 6.934c-2.12.072-4.14.588-5.965 1.64c-3.648 2.106-5.826 5.97-6.805 10.543c-.978 4.574-.875 10.013.1 16.1c.376 2.347.926 4.815 1.562 7.338c-2.503.71-4.916 1.47-7.136 2.318c-5.76 2.2-10.52 4.83-13.992 7.965S0 59.788 0 64s2.256 8.03 5.729 11.164C9.2 78.3 13.96 80.93 19.72 83.13c2.22.848 4.633 1.606 7.136 2.317c-.636 2.523-1.186 4.991-1.562 7.337c-.975 6.09-1.076 11.527-.098 16.1c.98 4.575 3.155 8.438 6.803 10.544s8.08 2.06 12.531.62c4.45-1.44 9.11-4.247 13.895-8.136c1.844-1.498 3.707-3.206 5.575-5.02c1.866 1.814 3.73 3.522 5.574 5.02c4.784 3.89 9.442 6.697 13.893 8.137c4.45 1.44 8.885 1.485 12.533-.62c3.648-2.107 5.826-5.97 6.805-10.544c.978-4.574.873-10.012-.102-16.1c-.376-2.347-.924-4.815-1.56-7.338c2.503-.71 4.916-1.47 7.136-2.317c5.76-2.199 10.52-4.83 13.992-7.965S128 68.212 128 64s-2.256-8.028-5.729-11.162c-3.472-3.134-8.232-5.766-13.992-7.965c-2.22-.847-4.631-1.608-7.134-2.318c.636-2.523 1.184-4.992 1.56-7.338c.975-6.087 1.076-11.526.098-16.1S99.649 10.68 96 8.574s-8.08-2.06-12.531-.62c-4.45 1.44-9.11 4.248-13.895 8.136c-1.844 1.5-3.71 3.208-5.577 5.022c-1.867-1.813-3.73-3.523-5.574-5.022c-4.783-3.888-9.439-6.698-13.889-8.138c-2.226-.72-4.448-1.09-6.57-1.02zm.256 5.545c1.33-.038 2.854.207 4.6.77c3.49 1.13 7.675 3.57 12.093 7.16c1.702 1.382 3.443 2.982 5.197 4.69c-3.664 3.96-7.298 8.463-10.814 13.434c-6.063.56-11.78 1.456-17.043 2.65c-.6-2.374-1.116-4.68-1.463-6.847c-.9-5.62-.92-10.465-.152-14.053c.767-3.587 2.202-5.767 4.144-6.888c.971-.56 2.108-.88 3.438-.916m51.559 0c1.33.037 2.467.355 3.438.916c1.942 1.12 3.377 3.3 4.144 6.887s.75 8.433-.15 14.054c-.347 2.165-.862 4.474-1.463 6.846c-5.262-1.193-10.98-2.09-17.045-2.649c-3.516-4.97-7.152-9.474-10.816-13.435c1.754-1.707 3.497-3.308 5.199-4.69c4.418-3.592 8.603-6.028 12.094-7.158c1.745-.564 3.27-.808 4.6-.77zM64 29.102a124 124 0 0 1 7.44 8.926c-2.444-.11-4.917-.184-7.44-.184c-2.522 0-4.995.073-7.44.184A123 123 0 0 1 64 29.102m0 14.308c3.985 0 7.87.157 11.634.44a155 155 0 0 1 6.199 9.853A155 155 0 0 1 87.267 64a155 155 0 0 1-5.434 10.295a155 155 0 0 1-6.2 9.856c-3.763.283-7.648.441-11.632.441a155 155 0 0 1-11.638-.44a155 155 0 0 1-6.197-9.857a155 155 0 0 1-5.434-10.293a155 155 0 0 1 5.436-10.297a155 155 0 0 1 6.2-9.855a155 155 0 0 1 11.633-.44zm-18.773 1.163a164 164 0 0 0-3.88 6.35a164 164 0 0 0-3.56 6.534a124 124 0 0 1-4.012-10.906c3.59-.8 7.412-1.47 11.452-1.978m37.547 0a124 124 0 0 1 11.452 1.978a124 124 0 0 1-4.012 10.908a164 164 0 0 0-3.56-6.537a164 164 0 0 0-3.88-6.35zm-54.409 3.341c1.598 5.154 3.681 10.554 6.23 16.084c-2.55 5.532-4.63 10.934-6.23 16.088c-2.355-.665-4.61-1.374-6.66-2.156c-5.319-2.03-9.524-4.438-12.248-6.898c-2.723-2.458-3.893-4.79-3.893-7.033c0-2.242 1.17-4.572 3.893-7.03c2.724-2.46 6.93-4.867 12.248-6.897c2.05-.783 4.305-1.493 6.66-2.159zm71.27 0c2.356.666 4.612 1.376 6.66 2.16c5.32 2.03 9.525 4.436 12.249 6.895c2.723 2.46 3.893 4.79 3.893 7.031c0 2.243-1.17 4.575-3.893 7.033c-2.724 2.46-6.93 4.866-12.248 6.897c-2.05.782-4.307 1.49-6.662 2.156c-1.598-5.153-3.68-10.553-6.227-16.084c2.549-5.532 4.63-10.933 6.229-16.088zM64 52.592A11.41 11.41 0 0 0 52.592 64a11.41 11.41 0 0 0 11.409 11.408A11.41 11.41 0 0 0 75.409 64A11.41 11.41 0 0 0 64 52.592m26.214 17.951a124 124 0 0 1 4.01 10.908c-3.588.8-7.41 1.471-11.448 1.98a164 164 0 0 0 3.877-6.353a164 164 0 0 0 3.56-6.535zm-52.427.002a164 164 0 0 0 3.56 6.533a166 166 0 0 0 3.88 6.352a123 123 0 0 1-11.452-1.979a124 124 0 0 1 4.012-10.906M32.252 86.82c5.262 1.192 10.98 2.087 17.045 2.646c3.516 4.973 7.152 9.477 10.816 13.44c-1.754 1.705-3.495 3.305-5.197 4.69c-4.418 3.59-8.607 6.03-12.098 7.16c-3.49 1.128-6.095.973-8.037-.148c-1.942-1.122-3.375-3.3-4.142-6.887c-.768-3.588-.749-8.433.152-14.056c.347-2.165.86-4.473 1.461-6.845m63.495 0c.6 2.372 1.116 4.68 1.463 6.845c.9 5.623.92 10.468.152 14.056c-.767 3.588-2.202 5.767-4.144 6.89c-1.942 1.12-4.546 1.273-8.037.143s-7.676-3.57-12.094-7.16c-1.701-1.383-3.444-2.983-5.197-4.69c3.664-3.96 7.298-8.466 10.814-13.438c6.063-.559 11.78-1.454 17.043-2.646M56.56 89.972a164 164 0 0 0 7.441.186a164 164 0 0 0 7.442-.186a123 123 0 0 1-7.442 8.93a123 123 0 0 1-7.44-8.93z\"/>",
        "vb": "0 0 128 128",
        "strw": "0"
    },
    "cb:maximize": {
        "svg": "<path fill=\"currentColor\" d=\"M20 2v2h6.586L18 12.582L19.414 14L28 5.414V12h2V2zm-6 17.416L12.592 18L4 26.586V20H2v10h10v-2H5.414z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "cb:close": {
        "svg": "<path fill=\"currentColor\" d=\"M17.414 16L24 9.414L22.586 8L16 14.586L9.414 8L8 9.414L14.586 16L8 22.586L9.414 24L16 17.414L22.586 24L24 22.586z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "emojione:artist-palette": {
        "svg": "<path fill=\"#f6c799\" d=\"M49.6 23.6C58.4 8.5 40.3-1.3 17.3 9.2C3.2 15.7-6.6 35.7 13 52.9c13.9 12.2 49 5.3 49-8.7c0-15.5-21.7-4.8-12.4-20.6m4.9 24.6c-2.8 2.4-7.2 2.4-10 0s-2.8-4.5 0-6.9s7.2-2.4 10 0s2.7 4.5 0 6.9\"/><path fill=\"#2caece\" d=\"M33.2 45.1c-3.1-2.4-8-2.4-11.1 0s-3.1 6.2 0 8.6s8 2.4 11.1 0c3-2.4 3-6.3 0-8.6\"/><path fill=\"#fdf516\" d=\"M19.6 33.6c-3.4-1.6-8-.6-10.4 2.3S7.6 42.4 11 44s8 .6 10.4-2.3s1.6-6.5-1.8-8.1\"/><path fill=\"#f55\" d=\"M17 20.6c-2.9-1.6-7.2-.9-9.4 1.6c-2.3 2.5-1.7 5.8 1.2 7.3c2.9 1.6 7.2.9 9.4-1.6s1.7-5.7-1.2-7.3\"/><path fill=\"#83bf4f\" d=\"M28.4 10.8c-2.8-1.6-6.9-1-9.1 1.4s-1.8 5.5 1.1 7.1c2.8 1.6 6.9 1 9.1-1.4s1.7-5.6-1.1-7.1\"/><path fill=\"#9156b7\" d=\"M44.7 9.7c-2.2-1.8-5.9-2.2-8.5-1c-2.5 1.2-2.8 3.7-.6 5.5s5.9 2.2 8.5 1c2.5-1.3 2.7-3.7.6-5.5\"/><path fill=\"#947151\" d=\"M40 42.1c-1.9 2.1-11.5 4-11.5 4s3.8-3.5 5.5-9.2c.8-2.7 4.7-2.7 6.4-1.2c1.7 1.4 1.5 4.3-.4 6.4\"/><path fill=\"#666\" d=\"M58.7 12.3c1-.1 2.9 1.6 3 2.5C62 19.1 44 34.5 44 34.5L41 32s13.3-19.4 17.7-19.7\"/><path fill=\"#ccc\" d=\"m38.4 34.9l3 2.5l2.6-2.9l-3-2.5z\"/>",
        "vb": "0 0 64 64",
        "strw": "0"
    },
    "fluent-color:text-edit-style-16": {
        "svg": "<g fill=\"none\"><path fill=\"url(#fluentColorTextEditStyle160)\" fill-rule=\"evenodd\" d=\"M5 2a.5.5 0 0 1 .471.332l2.5 7a.5.5 0 0 1-.942.336L5 4L2.971 9.668a.5.5 0 0 1-.942-.336l2.5-7A.5.5 0 0 1 5 2\" clip-rule=\"evenodd\"/><path fill=\"url(#fluentColorTextEditStyle161)\" fill-rule=\"evenodd\" d=\"M6.5 7h-3V6h3z\" clip-rule=\"evenodd\"/><path fill=\"url(#fluentColorTextEditStyle162)\" d=\"m8.733 12.975l4.596-4.596l-2.707-2.707l-4.597 4.596l-.011 2.718z\"/><path fill=\"url(#fluentColorTextEditStyle163)\" d=\"M7.504 13.61a2.5 2.5 0 0 0 1.278-.684s-.912-.21-1.705-1.003a3.9 3.9 0 0 1-1.002-1.705a2.5 2.5 0 0 0-.684 1.278l-.381 1.906a.5.5 0 0 0 .588.588z\"/><path fill=\"url(#fluentColorTextEditStyle164)\" d=\"M13.44 5.562a1.915 1.915 0 0 0-2.708 0L9.61 6.681l2.707 2.708L13.44 8.27a1.915 1.915 0 0 0 0-2.707\"/><path fill=\"url(#fluentColorTextEditStyle165)\" d=\"m12.091 9.616l.884-.883s-.914-.207-1.707-1a3.9 3.9 0 0 1-1-1.708l-.884.884s.207.914 1 1.707a3.9 3.9 0 0 0 1.707 1\"/><defs><linearGradient id=\"fluentColorTextEditStyle160\" x1=\"2\" x2=\"4.324\" y1=\"2.471\" y2=\"11.129\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#0FAFFF\"/><stop offset=\".677\" stop-color=\"#0078D4\"/><stop offset=\".84\" stop-color=\"#0057AA\"/><stop offset=\"1\" stop-color=\"#0057AA\"/></linearGradient><linearGradient id=\"fluentColorTextEditStyle161\" x1=\"2\" x2=\"4.324\" y1=\"2.471\" y2=\"11.129\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#0FAFFF\"/><stop offset=\".677\" stop-color=\"#0078D4\"/><stop offset=\".84\" stop-color=\"#0057AA\"/><stop offset=\"1\" stop-color=\"#0057AA\"/></linearGradient><linearGradient id=\"fluentColorTextEditStyle162\" x1=\"10.06\" x2=\"11.178\" y1=\"7.717\" y2=\"10.787\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#FFA43D\"/><stop offset=\"1\" stop-color=\"#FB5937\"/></linearGradient><linearGradient id=\"fluentColorTextEditStyle163\" x1=\"3.909\" x2=\"6.984\" y1=\"11.407\" y2=\"14.461\" gradientUnits=\"userSpaceOnUse\"><stop offset=\".255\" stop-color=\"#FFD394\"/><stop offset=\"1\" stop-color=\"#FF921F\"/></linearGradient><linearGradient id=\"fluentColorTextEditStyle164\" x1=\"13.229\" x2=\"11.872\" y1=\"5.767\" y2=\"7.043\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F97DBD\"/><stop offset=\"1\" stop-color=\"#DD3CE2\"/></linearGradient><linearGradient id=\"fluentColorTextEditStyle165\" x1=\"11.242\" x2=\"8.704\" y1=\"8.483\" y2=\"7.373\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#FF921F\"/><stop offset=\"1\" stop-color=\"#FFE994\"/></linearGradient></defs></g>",
        "vb": "0 0 16 16",
        "strw": "0"
    },
    "vscode-icons:file-type-style": {
        "svg": "<path fill=\"#1572b6\" d=\"M5.902 27.201L3.656 2h24.688l-2.249 25.197L15.985 30z\"/><path fill=\"#33a9dc\" d=\"m16 27.858l8.17-2.265l1.922-21.532H16z\"/><path fill=\"#ebebeb\" d=\"M7.347 14.714c2.254 0 2.572-.913 2.572-1.734a10 10 0 0 0-.256-1.966a10 10 0 0 1-.253-1.945C9.409 6.913 11.314 6 14.013 6h.667v1.289h-.571c-1.874 0-2.507.749-2.507 2.014a7.5 7.5 0 0 0 .222 1.663a8.2 8.2 0 0 1 .222 1.779c.031 1.5-.858 2.248-2.288 2.531v.046a2.4 2.4 0 0 1 2.288 2.554a8 8 0 0 1-.222 1.779a7.7 7.7 0 0 0-.222 1.688c0 1.31.729 2.037 2.507 2.037h.571v1.287h-.667c-2.634 0-4.6-.843-4.6-3.208a10 10 0 0 1 .253-1.92a9.6 9.6 0 0 0 .256-1.9c0-.727-.318-1.734-2.572-1.734Z\"/><path fill=\"#fff\" d=\"M24.68 15.906c-2.254 0-2.572 1.007-2.572 1.733a10 10 0 0 0 .253 1.9a10 10 0 0 1 .256 1.92c0 2.365-2 3.209-4.6 3.209h-.667v-1.29h.54c1.778-.023 2.538-.727 2.538-2.037a10.5 10.5 0 0 0-.222-1.688a8 8 0 0 1-.253-1.779a2.445 2.445 0 0 1 2.285-2.553v-.046a2.44 2.44 0 0 1-2.285-2.53a8.2 8.2 0 0 1 .253-1.779a10 10 0 0 0 .219-1.666c0-1.264-.667-1.991-2.507-2.014h-.571V6h.636c2.7 0 4.635.913 4.635 3.068a10 10 0 0 1-.256 1.945a10.4 10.4 0 0 0-.253 1.966c0 .82.318 1.733 2.572 1.733Z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "bubbles:family-child-outline": {
        "svg": "<path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M16.5 5.25a3 3 0 1 0 6 0a3 3 0 0 0-6 0m1.312 15l.188 1.5h3l.75-6h1.5V13.5a3.75 3.75 0 0 0-6-3M1.5 5.25a3 3 0 1 0 6 0a3 3 0 0 0-6 0m4.688 15L6 21.75H3l-.75-6H.75V13.5a3.75 3.75 0 0 1 6-3m3-1.5a2.25 2.25 0 1 0 4.5 0a2.25 2.25 0 0 0-4.5 0M12 12.75a3.75 3.75 0 0 0-3.75 3.75v.75h1.5l.75 4.5H12m0-9a3.75 3.75 0 0 1 3.75 3.75v.75h-1.5l-.75 4.5H12\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "flat-color-icons:key": {
        "svg": "<g fill=\"#FFA000\"><path d=\"m30 41l-4 4h-4l-4-4V21h12v8l-2 2l2 2v2l-2 2l2 2z\"/><path d=\"M38 7.8c-.5-1.8-2-3.1-3.7-3.6C31.9 3.7 28.2 3 24 3s-7.9.7-10.3 1.2C12 4.7 10.5 6 10 7.8c-.5 1.7-1 4.1-1 6.7s.5 5 1 6.7c.5 1.8 1.9 3.1 3.7 3.5c2.4.6 6.1 1.3 10.3 1.3s7.9-.7 10.3-1.2c1.8-.4 3.2-1.8 3.7-3.5s1-4.1 1-6.7c0-2.7-.5-5.1-1-6.8M29 13H19c-1.1 0-2-.9-2-2V9c0-.6 3.1-1 7-1s7 .4 7 1v2c0 1.1-.9 2-2 2\"/></g><path fill=\"#D68600\" d=\"M23 26h2v19h-2z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "aws:developer-tools": {
        "svg": "<g fill=\"none\" fill-rule=\"evenodd\"><rect width=\"64\" height=\"64\" x=\"5\" y=\"5\" fill=\"#C925D1\"/><rect width=\"72\" height=\"72\" x=\"1\" y=\"1\" stroke=\"#879196\" stroke-width=\"2\"/><path fill=\"#FFF\" d=\"M42.0184887,37.7552949 C42.0184887,37.3052949 41.8434887,36.8832949 41.5254887,36.5652949 L26.0564887,21.0972949 C25.9304887,20.9712949 25.7644887,20.9022949 25.5864887,20.9022949 C25.4094887,20.9022949 25.2424887,20.9712949 25.1164887,21.0972949 L21.0964887,25.1162949 C20.8374887,25.3762949 20.8374887,25.7972949 21.0974887,26.0572949 L36.5654887,41.5252949 C37.2214887,42.1802949 38.2894887,42.1812949 38.9454887,41.5252949 L41.5254887,38.9452949 C41.8424887,38.6272949 42.0184887,38.2042949 42.0184887,37.7552949 L42.0184887,37.7552949 Z M10.6294887,12.5642949 L20.7454887,22.6402949 L22.6404887,20.7452949 L13.1574887,11.2242949 C12.7684887,10.8332949 12.4744887,10.3512949 12.3074887,9.82929486 L10.9064887,5.45829486 C10.7784887,5.06129486 10.4864887,4.73329486 10.1054887,4.56029486 L5.30748868,2.38429486 C4.74748868,2.13029486 4.07948868,2.25229486 3.64448868,2.68729486 L2.68748868,3.64429486 C2.25248868,4.07929486 2.13048868,4.74729486 2.38448868,5.30729486 L4.56048868,10.1052949 C4.73348868,10.4862949 5.06048868,10.7772949 5.45848868,10.9052949 L10.6294887,12.5642949 Z M42.9394887,35.1512949 C43.6344887,35.8462949 44.0184887,36.7712949 44.0184887,37.7552949 C44.0184887,38.7382949 43.6344887,39.6632949 42.9394887,40.3592949 L40.3594887,42.9392949 C39.6414887,43.6562949 38.6984887,44.0162949 37.7554887,44.0162949 C36.8124887,44.0162949 35.8694887,43.6572949 35.1514887,42.9392949 L19.6824887,27.4712949 C18.7654887,26.5522949 18.6624887,25.1272949 19.3664887,24.0902949 L9.55948868,14.3212949 L4.84748868,12.8102949 C3.91348868,12.5102949 3.14448868,11.8252949 2.73948868,10.9322949 L0.562488683,6.13329486 C-0.0325113166,4.81829486 0.252488683,3.25029486 1.27348868,2.23029486 L2.23048868,1.27329486 C3.24948868,0.253294857 4.81748868,-0.0337051432 6.13348868,0.563294857 L10.9314887,2.73929486 C11.8254887,3.14429486 12.5094887,3.91229486 12.8104887,4.84729486 L14.2114887,9.21929486 C14.2834887,9.44129486 14.4094887,9.64729486 14.5754887,9.81429486 L24.0914887,19.3692949 C25.1164887,18.6922949 26.5844887,18.7952949 27.4714887,19.6832949 L42.9394887,35.1512949 Z M23.7284887,15.6802949 L29.0504887,9.98429486 C29.2534887,9.76729486 29.3494887,9.47129486 29.3124887,9.17529486 C29.0604887,7.19829486 29.7194887,5.26029486 31.1204887,3.85929486 C32.6674887,2.31129486 34.8834887,1.69229486 36.9634887,2.14029486 L33.1824887,5.92229486 C32.5294887,6.57529486 32.1694887,7.44329486 32.1694887,8.36729486 C32.1694887,9.29129486 32.5294887,10.1602949 33.1824887,10.8132949 C34.5324887,12.1612949 36.7264887,12.1602949 38.0744887,10.8132949 L41.8554887,7.03129486 C42.3014887,9.11329486 41.6834887,11.3292949 40.1364887,12.8752949 C38.7354887,14.2762949 36.7964887,14.9352949 34.8194887,14.6842949 C34.5094887,14.6412949 34.2044887,14.7502949 33.9864887,14.9692949 L29.9774887,18.9782949 L31.3914887,20.3922949 L35.0664887,16.7172949 C37.4864887,16.8812949 39.8284887,16.0132949 41.5504887,14.2892949 C44.0504887,11.7892949 44.7064887,7.96129486 43.1804887,4.76329486 C43.0404887,4.47029486 42.7674887,4.26329486 42.4474887,4.20829486 C42.1324887,4.15329486 41.8014887,4.25629486 41.5714887,4.48629486 L36.6604887,9.39929486 C36.0914887,9.96829486 35.1644887,9.96629486 34.5964887,9.39929486 C34.0284887,8.83029486 34.0284887,7.90529486 34.5964887,7.33629486 L39.5084887,2.42429486 C39.7384887,2.19429486 39.8414887,1.86829486 39.7874887,1.54729486 C39.7324887,1.22829486 39.5254887,0.954294857 39.2324887,0.815294857 C36.0354887,-0.709705143 32.2064887,-0.0557051432 29.7054887,2.44529486 C27.9774887,4.17429486 27.1084887,6.52229486 27.2814887,8.95029486 L22.2674887,14.3152949 L23.7284887,15.6802949 Z M19.7424887,31.6662949 L16.7144887,35.0342949 C16.8904887,37.4652949 16.0214887,39.8182949 14.2894887,41.5502949 C12.6884887,43.1512949 10.5414887,43.9962949 8.36748868,43.9962949 C7.14448868,43.9962949 5.91348868,43.7302949 4.76348868,43.1812949 C4.47048868,43.0412949 4.26348868,42.7682949 4.20848868,42.4482949 C4.15348868,42.1282949 4.25748868,41.8012949 4.48748868,41.5712949 L9.39848868,36.6592949 C9.67448868,36.3832949 9.82648868,36.0172949 9.82648868,35.6282949 C9.82648868,35.2392949 9.67448868,34.8732949 9.39848868,34.5972949 C8.83048868,34.0302949 7.90548868,34.0292949 7.33548868,34.5972949 L2.42448868,39.5092949 C2.19448868,39.7382949 1.86148868,39.8412949 1.54848868,39.7872949 C1.22848868,39.7332949 0.955488683,39.5262949 0.815488683,39.2332949 C-0.710511317,36.0352949 -0.0545113166,32.2072949 2.44548868,29.7062949 C4.17348868,27.9782949 6.52848868,27.1092949 8.94948868,27.2802949 L14.3154887,22.2672949 L15.6804887,23.7292949 L9.98448868,29.0502949 C9.76648868,29.2542949 9.46948868,29.3512949 9.17548868,29.3122949 C7.19948868,29.0582949 5.25948868,29.7192949 3.85948868,31.1202949 C2.31248868,32.6672949 1.69448868,34.8822949 2.14048868,36.9642949 L5.92148868,33.1832949 C7.26948868,31.8362949 9.46248868,31.8342949 10.8124887,33.1832949 C11.4654887,33.8362949 11.8264887,34.7042949 11.8264887,35.6282949 C11.8264887,36.5522949 11.4654887,37.4212949 10.8124887,38.0742949 L7.03148868,41.8552949 C9.10948868,42.3002949 11.3284887,41.6832949 12.8754887,40.1362949 C14.2764887,38.7352949 14.9354887,36.7972949 14.6834887,34.8202949 C14.6474887,34.5312949 14.7374887,34.2412949 14.9314887,34.0252949 L18.2534887,30.3292949 L19.7424887,31.6662949 Z\" transform=\"translate(15 14)\"/></g>",
        "vb": "0 0 74 74",
        "strw": "0"
    },
    "vscode-icons:file-type-word": {
        "svg": "<defs><linearGradient id=\"vscodeIconsFileTypeWord0\" x1=\"4.494\" x2=\"13.832\" y1=\"-1712.086\" y2=\"-1695.914\" gradientTransform=\"translate(0 1720)\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#2368c4\"/><stop offset=\".5\" stop-color=\"#1a5dbe\"/><stop offset=\"1\" stop-color=\"#1146ac\"/></linearGradient></defs><path fill=\"#41a5ee\" d=\"M28.806 3H9.705a1.19 1.19 0 0 0-1.193 1.191V9.5l11.069 3.25L30 9.5V4.191A1.19 1.19 0 0 0 28.806 3\"/><path fill=\"#2b7cd3\" d=\"M30 9.5H8.512V16l11.069 1.95L30 16Z\"/><path fill=\"#185abd\" d=\"M8.512 16v6.5l10.418 1.3L30 22.5V16Z\"/><path fill=\"#103f91\" d=\"M9.705 29h19.1A1.19 1.19 0 0 0 30 27.809V22.5H8.512v5.309A1.19 1.19 0 0 0 9.705 29\"/><path d=\"M16.434 8.2H8.512v16.25h7.922a1.2 1.2 0 0 0 1.194-1.191V9.391A1.2 1.2 0 0 0 16.434 8.2\" opacity=\".1\"/><path d=\"M15.783 8.85H8.512V25.1h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191\" opacity=\".2\"/><path d=\"M15.783 8.85H8.512V23.8h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191\" opacity=\".2\"/><path d=\"M15.132 8.85h-6.62V23.8h6.62a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191\" opacity=\".2\"/><path fill=\"url(#vscodeIconsFileTypeWord0)\" d=\"M3.194 8.85h11.938a1.193 1.193 0 0 1 1.194 1.191v11.918a1.193 1.193 0 0 1-1.194 1.191H3.194A1.19 1.19 0 0 1 2 21.959V10.041A1.19 1.19 0 0 1 3.194 8.85\"/><path fill=\"#fff\" d=\"M6.9 17.988q.035.276.046.481h.028q.015-.195.065-.47c.05-.275.062-.338.089-.465l1.255-5.407h1.624l1.3 5.326a8 8 0 0 1 .162 1h.022a8 8 0 0 1 .135-.975l1.039-5.358h1.477l-1.824 7.748h-1.727l-1.237-5.126q-.054-.222-.122-.578t-.084-.52h-.021q-.021.189-.084.561t-.1.552L7.78 19.871H6.024L4.19 12.127h1.5l1.131 5.418a5 5 0 0 1 .079.443\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "flat-color-icons:template": {
        "svg": "<path fill=\"#BBDEFB\" d=\"M4 7h40v34H4z\"/><path fill=\"#3F51B5\" d=\"M9 12h30v5H9z\"/><path fill=\"#2196F3\" d=\"M9 21h13v16H9zm17 0h13v16H26z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:services": {
        "svg": "<path fill=\"#E65100\" d=\"M25.6 34.4c.1-.4.1-.9.1-1.4s0-.9-.1-1.4l2.8-2c.3-.2.4-.6.2-.9l-2.7-4.6c-.2-.3-.5-.4-.8-.3L22 25.3c-.7-.6-1.5-1-2.4-1.4l-.3-3.4c0-.3-.3-.6-.6-.6h-5.3c-.3 0-.6.3-.6.6l-.4 3.5c-.9.3-1.6.8-2.4 1.4L6.9 24c-.3-.1-.7 0-.8.3l-2.7 4.6c-.2.3-.1.7.2.9l2.8 2c-.1.4-.1.9-.1 1.4s0 .9.1 1.4l-2.8 2c-.3.2-.4.6-.2.9l2.7 4.6c.2.3.5.4.8.3L10 41c.7.6 1.5 1 2.4 1.4l.3 3.4c0 .3.3.6.6.6h5.3c.3 0 .6-.3.6-.6l.3-3.4c.9-.3 1.6-.8 2.4-1.4l3.1 1.4c.3.1.7 0 .8-.3l2.7-4.6c.2-.3.1-.7-.2-.9zM16 38c-2.8 0-5-2.2-5-5s2.2-5 5-5s5 2.2 5 5s-2.2 5-5 5\"/><path fill=\"#FFA000\" d=\"M41.9 15.3c.1-.5.1-.9.1-1.3s0-.8-.1-1.3l2.5-1.8c.3-.2.3-.5.2-.8l-2.5-4.3c-.2-.3-.5-.4-.8-.2l-2.9 1.3c-.7-.5-1.4-.9-2.2-1.3l-.3-3.1c.1-.3-.1-.5-.4-.5h-4.9c-.3 0-.6.2-.6.5l-.3 3.1c-.8.3-1.5.7-2.2 1.3l-2.9-1.3c-.3-.1-.6 0-.8.2l-2.5 4.3c-.2.3-.1.6.2.8l2.5 1.8V14c0 .4 0 .8.1 1.3l-2.5 1.8c-.3.2-.3.5-.2.8l2.5 4.3c.2.3.5.4.8.2l2.9-1.3c.7.5 1.4.9 2.2 1.3l.3 3.1c0 .3.3.5.6.5h4.9c.3 0 .6-.2.6-.5l.3-3.1c.8-.3 1.5-.7 2.2-1.3l2.9 1.3c.3.1.6 0 .8-.2l2.5-4.3c.2-.3.1-.6-.2-.8zM33 19c-2.8 0-5-2.2-5-5s2.2-5 5-5s5 2.2 5 5s-2.2 5-5 5\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:tree-structure": {
        "svg": "<path fill=\"#90CAF9\" d=\"m36.9 13.8l-1.8-3.6L7.5 24l27.6 13.8l1.8-3.6L16.5 24z\"/><path fill=\"#D81B60\" d=\"M6 18h12v12H6z\"/><path fill=\"#2196F3\" d=\"M30 6h12v12H30zm0 24h12v12H30z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:export": {
        "svg": "<path fill=\"#FFCCBC\" d=\"M7 40V8c0-2.2 1.8-4 4-4h24c2.2 0 4 1.8 4 4v32c0 2.2-1.8 4-4 4H11c-2.2 0-4-1.8-4-4\"/><g fill=\"#FF5722\"><path d=\"M42.7 24L32 33V15z\"/><path d=\"M14 21h23v6H14z\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "flat-color-icons:import": {
        "svg": "<path fill=\"#F8BBD0\" d=\"M7 40V8c0-2.2 1.8-4 4-4h24c2.2 0 4 1.8 4 4v32c0 2.2-1.8 4-4 4H11c-2.2 0-4-1.8-4-4\"/><g fill=\"#E91E63\"><path d=\"M13.3 24L24 15v18z\"/><path d=\"M19 21h23v6H19z\"/></g>",
        "vb": "0 0 48 48",
        "strw": "0"
    }
}
FX.setIcons(usedIcons);

export const $icons = {
    "weddingCouple": weddingCouple,
    "parents": parents
}
