{"fields": {"type": "field", "label": "fields", "icon": "flat-color-icons:services:32", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:$root-field", "$$$": {}, "items": [{"type": "field", "label": "ООО \"Твой компьютер\"", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:$root-field", "sort": 0, "is": "label", "icon": "carbon:label", "class": "fl", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K271RN1DNDBX981J6PTM1SAQ", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "tabs", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:$root-field", "sort": 1, "is": "tabs", "icon": "carbon:collapse-all", "class": "", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PDC1TFGCKRKKHSX7H3RV", "$$$": {}, "items": [{"type": "field", "label": "Прай<PERSON>ы", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PDC1TFGCKRKKHSX7H3RV", "sort": 0, "class": "", "is": "tab", "icon": "fluent-mdl2:folder-horizontal", "value": {"template": "{\n    \"columns\": [\n        {\n            \"field\": \"id\",\n            \"header\": \"ID\",\n            \"width\": 64\n        },\n        {\n            \"field\": \"date\",\n            \"header\": \"Date\",\n            \"width\": 150,\n            \"typeColumn\": \"input\",\n            \"typeInput\": \"date\"\n        },\n        {\n            \"field\": \"name\",\n            \"header\": \"Название\",\n            \"width\": 150,\n            \"typeColumn\": \"input\"\n        },\n        {\n            \"field\": \"kol\",\n            \"header\": \"Количество\",\n            \"width\": 100,\n            \"typeColumn\": \"input\",\n            \"textAlign\": \"right\",\n            \"footerStyle\": \"justify-content: end;\"\n        },\n        {\n            \"field\": \"price\",\n            \"header\": \"Цена\",\n            \"width\": 100,\n            \"typeColumn\": \"input\",\n            \"textAlign\": \"right\",\n            \"footerStyle\": \"justify-content: end;\"\n        },\n        {\n            \"field\": \"total\",\n            \"header\": \"Итого\",\n            \"width\": 120,\n            \"typeColumn\": \"span\",\n            \"textAlign\": \"right\",\n            \"footerStyle\": \"justify-content: end;\",\n            \"calc\": \"(e) => (e.kol || 0) * (e.price || 0)\"\n        },\n        {\n            \"field\": \"note\",\n            \"header\": \"Примечание\",\n            \"minWidth\": 200,\n            \"width\": \"auto\",\n            \"typeColumn\": \"textarea\"\n        }\n    ],\n    \"footerCalculations\": {\n        \"id\": {\n            \"type\": \"count\"\n        },\n        \"kol\": {\n            \"type\": \"sum\",\n            \"decimals\": 2\n        },\n        \"total\": {\n            \"type\": \"sum\",\n            \"decimals\": 2\n        },\n        \"name\": {\n            \"type\": \"custom\",\n            \"customFn\": \"(data) => `Total: ${data.length} names`\"\n        }\n    },\n    \"options\": {\n        \"headerClass\": \"header\",\n        \"footerClass\": \"footer\",\n        \"sortColumns\": [\n            {\n                \"field\": \"date\",\n                \"direction\": \"desc\"\n            }\n        ]\n    }\n}", "templateId": "first-table"}, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PSM9F843P7NHBERBD52X", "$$$": {}, "items": [{"type": "field", "label": "Select price", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PSM9F843P7NHBERBD52X", "sort": 0, "is": "", "icon": "", "class": "", "hidden": true, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QXZ57ATC2ZZ83Q8Y8VPZ", "$$$": {"fIcon": "aws:application-integration", "fIconSize": "64", "hidden": true}, "items": [{"type": "field", "label": "new field", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QXZ57ATC2ZZ83Q8Y8VPZ", "sort": 0, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K202A129BNQKZSSFDMCC7DWG", "$$$": {"fIcon": "aws:artificial-intelligence", "fIconSize": "64"}, "items": [{"type": "field", "label": "new field", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K202A129BNQKZSSFDMCC7DWG", "sort": 0, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K202A1CWQHD8J7NHH8YS6MCT", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "new field", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K202A129BNQKZSSFDMCC7DWG", "sort": 1, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K202A1NVAY020MHH2WM21P61", "$$$": {}, "items": [], "checked": false}], "checked": false}, {"type": "field", "label": "new field", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QXZ57ATC2ZZ83Q8Y8VPZ", "sort": 1, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K202A1W3TPHG9DXXJXJ214XA", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "new field", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QXZ57ATC2ZZ83Q8Y8VPZ", "sort": 2, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K202A21YT2KR1GHHE5N16R75", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "new field", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QXZ57ATC2ZZ83Q8Y8VPZ", "sort": 3, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K26YFZQ02114STV4CZR2WEXY", "$$$": {}, "items": [], "checked": false}], "checked": false}, {"type": "field", "label": "table", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PSM9F843P7NHBERBD52X", "sort": 1, "is": "table", "icon": "carbon:table-split", "value": [], "class": "", "style": "max-height: calc(100vh - 130px)", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QY95WSCD4JVXAY3X5WWZ", "$$$": {"templateId": "sborka-price-1", "hideSearch": false, "hideAdd": true, "hide": "cbf", "tableHide": "cbf", "tableId": "sborka-price-1", "tableTemplateId": "sborka-price-1"}, "items": [], "checked": false}], "checked": false}, {"type": "field", "label": "Сборка", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PDC1TFGCKRKKHSX7H3RV", "sort": 1, "is": "tab", "icon": "fluent-mdl2:folder-horizontal", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "$$$": {}, "items": [{"type": "field", "label": "row", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "sort": 0, "is": "row", "icon": "lucide:rectangle-horizontal", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201RXBEBQ2Z6V9R48REKM5X", "$$$": {}, "items": [{"type": "field", "label": "Дата", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201RXBEBQ2Z6V9R48REKM5X", "sort": 0, "isType": "date", "value": "", "is": "input", "icon": "vaadin:input", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201S4KYW4DD31ATN4M4TMQY", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "№", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201RXBEBQ2Z6V9R48REKM5X", "sort": 1, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201S51ZH6W5SK6C7GB1GSR8", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "Скидка", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201RXBEBQ2Z6V9R48REKM5X", "sort": 2, "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201S5MD33YGPD5D19RQT74S", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "Сотрудник", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201RXBEBQ2Z6V9R48REKM5X", "sort": 3, "class": "flex", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201SSQ5ZA4R3NGVPJE7M4HB", "$$$": {"class": "flex"}, "items": [], "checked": false}, {"type": "field", "label": "$$$", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201RXBEBQ2Z6V9R48REKM5X", "sort": 4, "is": "input", "icon": "vaadin:input", "isType": "checkbox", "value": "on", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K28VWE9PB1PYTQP04Y02P0PN", "$$$": {}, "items": [], "checked": false}], "checked": false, "expanded": true}, {"type": "field", "label": "Кому", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "sort": 1, "class": "", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201T45EDMXRHQH6RS495G86", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "Сборка", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "sort": 2, "is": "label", "icon": "carbon:label", "class": "mt8", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K28V15D1ENE31Z75FBY94CFT", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "table", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "sort": 3, "is": "table", "icon": "carbon:table-split", "value": [], "class": "", "style": "max-height: calc(50vh - 190px)", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201TE5KVPVWPE5PEX8KFD5Y", "$$$": {"templateId": "sborka-zakaz", "tableId": "sborka-zakaz", "tableTemplateId": "sborka-zakaz", "tableHide": "cbe", "fstyle": ""}, "items": [], "checked": false}, {"type": "field", "label": "Прай<PERSON>ы", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "sort": 4, "is": "label", "icon": "carbon:label", "class": "mt8", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K28V161BBS4ZJQE8298AZ1RT", "$$$": {}, "items": [], "checked": false}, {"type": "field", "label": "table", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "sort": 5, "is": "table", "icon": "carbon:table-split", "value": [], "style": "max-height: calc(50vh - 190px)", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201TEHHG6MX21BFTMH0JAY8", "$$$": {"templateId": "sborka-price-1", "tableHide": "cbfe", "tableTemplateId": "sborka-price-1", "tableId": "sborka-price-2"}, "items": [], "checked": false}, {"type": "field", "label": "Примечание", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PT3D50W2QP090YW2R2P4", "sort": 6, "class": "", "style": "", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201V9Z2PH4EDPXDR14FGD7K", "$$$": {}, "items": [], "checked": false}], "checked": false, "expanded": true}, {"type": "field", "label": "Оплата", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PDC1TFGCKRKKHSX7H3RV", "sort": 2, "is": "tab", "icon": "fluent-mdl2:folder-horizontal", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PVA7W47QQ5KWTA7S5T05", "$$$": {}, "items": [{"type": "field", "label": "tab", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PVA7W47QQ5KWTA7S5T05", "sort": 0, "is": "table", "icon": "carbon:table-split", "style": "max-height: calc(100vh - 100px", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K28WP7H8E0VYME6EE8GMDDA0", "$$$": {"templateId": "sborka-oplata", "tableId": "sborka-oplata", "tableTemplateId": "sborka-oplata", "tableHide": "cb"}, "items": [], "checked": false}], "checked": false}, {"type": "field", "label": "Примечание", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201PDC1TFGCKRKKHSX7H3RV", "sort": 3, "is": "tab", "icon": "fluent-mdl2:folder-horizontal", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QKDH011TPMYCFJQZ7ZHQ", "$$$": {}, "items": [{"type": "field", "label": "table", "parentId": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K201QKDH011TPMYCFJQZ7ZHQ", "sort": 0, "is": "table", "icon": "carbon:table-split", "value": [], "style": "max-height: calc(100vh - 130px", "_id": "field:item:01K201NG3F7TRP6XNZYBZ0Y5EW:01K2914V985QRV726RWC0Z6AYJ", "$$$": {"templateId": "first-table", "tableId": "first-table", "tableTemplateId": "first-table"}, "items": [], "checked": false}], "checked": false}], "checked": false, "expanded": true}], "checked": false, "expanded": true}}