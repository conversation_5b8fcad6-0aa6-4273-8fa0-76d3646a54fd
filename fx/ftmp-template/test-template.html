<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Template Editor Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            height: 90vh;
        }
        h1 {
            margin: 0;
            padding: 20px;
            background: #007acc;
            color: white;
            font-size: 24px;
        }
        .editor-container {
            height: calc(100% - 64px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Form Template Editor - Test</h1>
        <div class="editor-container">
            <fx-ftmp-template id="template-editor"></fx-ftmp-template>
        </div>
    </div>

    <script type="module">
        // Простая имитация FX объекта для тестирования
        window.FX = {
            base: {
                dbLocal: {
                    allDocs: async (options) => {
                        console.log('allDocs called with:', options);
                        // Имитация базы данных с тестовыми шаблонами
                        if (options.startkey === 'ftmp-template:') {
                            return { 
                                rows: [
                                    {
                                        id: 'ftmp-template:test-form',
                                        doc: {
                                            _id: 'ftmp-template:test-form',
                                            type: 'ftmp-template',
                                            formData: {
                                                fields: {
                                                    _id: 'field:test:$root-field',
                                                    type: 'field',
                                                    label: 'Test Form',
                                                    is: 'row',
                                                    $$$: { label0: 'Test Form Template' },
                                                    items: [
                                                        {
                                                            _id: 'field:test:name',
                                                            type: 'field',
                                                            label: 'Name',
                                                            is: 'input',
                                                            isType: 'text'
                                                        }
                                                    ]
                                                }
                                            }
                                        }
                                    }
                                ]
                            };
                        }
                        return { rows: [] };
                    },
                    get: async (id) => {
                        console.log('get called with:', id);
                        if (id === 'ftmp-template:test-form') {
                            return {
                                _id: 'ftmp-template:test-form',
                                type: 'ftmp-template',
                                formData: {
                                    fields: {
                                        _id: 'field:test:$root-field',
                                        type: 'field',
                                        label: 'Test Form',
                                        is: 'row',
                                        $$$: { label0: 'Test Form Template' },
                                        items: [
                                            {
                                                _id: 'field:test:name',
                                                type: 'field',
                                                label: 'Name',
                                                is: 'input',
                                                isType: 'text'
                                            }
                                        ]
                                    }
                                }
                            };
                        }
                        throw new Error('Document not found');
                    },
                    put: async (doc) => {
                        console.log('Saving document:', doc);
                        return { ok: true, id: doc._id, rev: '1-abc' };
                    }
                },
                BS_ITEM: class {
                    constructor(args) {
                        this.doc = args.doc;
                        this._id = args._id;
                        this.type = args.type;
                        console.log('Created BS_ITEM:', args);
                    }
                    set _deleted(value) {
                        this.doc._deleted = value;
                        console.log('Marked as deleted:', this._id);
                    }
                },
                buildTree: async (flat, rootId, type, root) => {
                    console.log('buildTree called with:', { flat, rootId, type, root });
                    return root || { _id: rootId, items: Object.values(flat) };
                },
                fxSelected: {
                    _id: 'test-item'
                }
            },
            dates: () => ({ utc: new Date().toISOString() }),
            ulid: () => Math.random().toString(36).substr(2, 9)
        };

        // Имитация mainFtmp для тестирования
        const mockMainFtmp = {
            item: {
                fields: {
                    _id: 'field:main:$root-field',
                    type: 'field',
                    label: 'Main Form',
                    is: 'row',
                    $$$: { label0: 'Main Form from mainFtmp' },
                    items: [
                        {
                            _id: 'field:main:title',
                            type: 'field',
                            label: 'Title',
                            is: 'input',
                            isType: 'text',
                            $$$: { fIcon: 'carbon:text-font' }
                        },
                        {
                            _id: 'field:main:description',
                            type: 'field',
                            label: 'Description',
                            is: 'c-label'
                        }
                    ]
                }
            },
            $update: () => console.log('mainFtmp updated')
        };

        // Импортируем компонент
        import('./ftmp-template.js');
        
        // Инициализируем после загрузки
        document.addEventListener('DOMContentLoaded', () => {
            const editor = document.getElementById('template-editor');
            editor.base = FX.base;
            editor.mainFtmp = mockMainFtmp;
            
            console.log('Template editor initialized with mainFtmp:', mockMainFtmp);
        });
    </script>
</body>
</html>
