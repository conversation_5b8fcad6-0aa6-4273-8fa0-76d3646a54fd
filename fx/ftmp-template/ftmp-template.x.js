import { css } from '/fx.js';

export const $styles = css`
    :host {
        display: block;
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden!important;
        padding: 0;
        margin: 0;
    }
    label[selected] {
        background: var(--fx-color-selected, #007acc) !important;
        color: white !important;
    }
    .menu-dropdown {
        position: absolute;
        bottom: calc(100% + 36px);
        left: 4px;
        background: light-dark(white, var(--fx-panel-background));
        border: 1px solid var(--fx-border-color);
        border-radius: 6px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        min-width: 140px;
        padding: 4px;
        display: flex;
        flex-direction: column;
        /* gap: 2px; */
        animation: menuFadeIn 0.15s ease-out;
    }
    @keyframes menuFadeIn {
        from {
            opacity: 0;
            transform: translateY(-4px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;
// const usedIcons = {
// }
// FX.setIcons(usedIcons);
