import { css } from '/fx.js';

export const $styles = css`
    :host {
        display: block;
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden!important;
        padding: 0;
        margin: 0;
        z-index: 9999;
    }

    label[selected] {
        background: var(--fx-color-selected, #007acc) !important;
        color: white !important;
    }

    label.hover:hover {
        background: var(--fx-color-hover, #f0f0f0);
    }

    .template-list {
        border-right: 1px solid #ddd;
    }

    .template-item {
        transition: background-color 0.2s ease;
        cursor: pointer;
        user-select: none;
    }

    .template-controls {
        background: #f8f9fa;
        border-top: 2px solid #ddd;
    }

    fx-icon[txt] {
        width: 100%;
        justify-content: flex-start;
        padding: 8px 12px;
        margin: 2px 0;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }

    fx-icon[txt]:hover {
        background: rgba(0, 122, 204, 0.1);
    }
`

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;
// const usedIcons = {
// }
// FX.setIcons(usedIcons);
