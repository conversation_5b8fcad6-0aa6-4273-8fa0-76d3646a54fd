# Form Template Editor (ftmp-template)

Редактор шаблонов форм для системы fx. Позволяет создавать, редактировать, сохранять и загружать шаблоны форм в базе данных CouchDB с интеграцией с основным компонентом ftmp.

## Основные возможности

- ✅ **Интеграция с mainFtmp** - получение данных от основного компонента формы
- ✅ **JSON преобразование** - конвертация между форматами item и JSON
- ✅ **Кеширование данных** - работа с несохраненными изменениями
- ✅ **Apply функциональность** - применение изменений обратно к mainFtmp через buildTree
- ✅ **Создание новых шаблонов** на основе текущих данных
- ✅ **Редактирование существующих шаблонов**
- ✅ **Сохранение в базе данных** как единый JSON объект
- ✅ **Загрузка из базы данных** с кешированием
- ✅ **Удаление шаблонов** с очисткой кеша
- ✅ **Экспорт/импорт** JSON файлов
- ✅ **Визуальный конструктор** форм

## Архитектура и поток данных

### 1. Получение данных от mainFtmp
```javascript
// При инициализации компонент получает item от mainFtmp
if (this.mainFtmp?.item) {
    this.formData = this.convertItemToJSON(this.mainFtmp.item);
}
```

### 2. Преобразование в JSON
```javascript
// Конвертация item в чистый JSON объект
convertItemToJSON(item) {
    // Удаляет служебные поля, оставляет только структуру формы
    // Возвращает JSON объект для редактирования
}
```

### 3. Кеширование
```javascript
// Шаблоны кешируются для работы с несохраненными данными
this.templatesCache[templateName] = formData;
```

### 4. Применение изменений (Apply)
```javascript
// Преобразование JSON обратно в flat структуру
const { flat, root } = this.convertJSONToFlat(this.formData, selectedKey);

// Построение нового дерева
const newTree = await this.base.buildTree(flat, selectedKey + ':$root-field', 'field', root);

// Применение к mainFtmp
this.mainFtmp.item.fields = newTree;
```

## Свойства компонента

```javascript
static properties = {
    base: { type: Object },              // Ссылка на базовый объект FX
    templatesList: { type: Array },      // Список доступных шаблонов
    templatesCache: { type: Object },    // Кеш шаблонов
    selected: { type: String },          // Выбранный шаблон
    item: { type: Object },              // Текущий элемент формы
    templateId: { type: String },        // ID шаблона для загрузки
    formData: { type: Object },          // Данные формы в JSON формате
    mainFtmp: { type: Object }           // Ссылка на основной компонент ftmp
}
```

## Основные методы

### `convertItemToJSON(item)`
Преобразует item структуру в чистый JSON объект, удаляя служебные поля.

### `convertJSONToFlat(jsonData, selectedKey)`
Преобразует JSON обратно в flat структуру для buildTree.

### `getTemplatesList()`
Получает список шаблонов и кеширует их данные.

### `saveTemplate(templateId)`
Сохраняет текущий шаблон в базе данных и обновляет кеш.

### `applyTemplate()`
Применяет изменения к mainFtmp через преобразование в flat и buildTree.

### `loadTemplateToForm(templateId)`
Загружает шаблон в редактор, используя кеш если доступен.

### `createNewTemplate()`
Создает новый шаблон на основе данных mainFtmp или пустой.

## Использование

### Базовое использование с mainFtmp
```html
<fx-ftmp-template 
    .base=${this.base} 
    .mainFtmp=${this.ftmpComponent}>
</fx-ftmp-template>
```

### С предзагруженным шаблоном
```html
<fx-ftmp-template 
    .base=${this.base} 
    .mainFtmp=${this.ftmpComponent}
    templateId="my-template">
</fx-ftmp-template>
```

## Структура данных

### JSON формат шаблона
```json
{
  "fields": {
    "_id": "field:demo:$root-field",
    "type": "field",
    "label": "Demo Form",
    "is": "row",
    "$$$": {
      "label0": "Demo Form Template",
      "class": "p8",
      "style": "background: #f5f5f5;"
    },
    "items": [
      {
        "_id": "field:demo:input1",
        "type": "field",
        "label": "Input Field",
        "is": "input",
        "isType": "text"
      }
    ]
  }
}
```

### Документ в базе данных
```json
{
  "_id": "ftmp-template:template-name",
  "type": "ftmp-template",
  "created": "2024-01-01T00:00:00.000Z",
  "updated": "2024-01-01T00:00:00.000Z",
  "formData": { /* JSON данные формы */ }
}
```

## Интерфейс пользователя

- **Список шаблонов** - слева, с кешированными данными
- **Поле ввода имени** - для нового шаблона
- **Кнопки управления**:
  - `New Template` - создать новый на основе mainFtmp
  - `Save Template` - сохранить в базу
  - `Delete Template` - удалить из базы
  - `Apply Template` - применить к mainFtmp
  - `Export Template` - экспорт в JSON
  - `Import Template` - импорт из JSON
- **Редактор формы** - справа, визуальный конструктор

## Тестирование

Откройте `test-template.html` для тестирования с имитацией окружения FX и mockMainFtmp.
