import { FxElement, html } from '/fx.js';
import { $styles } from './ftmp-template.x.js';
import '../button/button.js';
import '../splitter/splitter.js';
import '../ftmp/ftmp.js';

export class FxFtmpTemplate extends FxElement {
    static properties = {
        base: { type: Object },
        data: { type: Object },
        templatesList: { type: Array, default: [] },
        templatesCache: { type: Object, default: {} },
        selected: { type: String, default: '' },
        item: { type: Object },
        templateId: { type: String, default: '' },
        formData: { type: Object },
        mainFtmp: { type: Object },
        flatItems: { type: Object, default: {} },
        allItems: { type: Array, default: [] }
    }
    get ftmpComponent() { return this.$qs('fx-ftmp') }
    get input() { return this.$qs('input') }

    async firstUpdated() {
        super.firstUpdated();
        this.async(async () => {
            FX.closeDD();
            this.base = FX.base || {};
            this.templatesCache = {};
            this.templatesList = await this.getTemplatesList();
            if (this.mainFtmp?.item) {
                this.allItems = []
                this.mainFtmp.all.forEach(i => {
                    let doc = { ...i.doc };
                    let $$$ = { ...doc.$$$ };
                    delete doc.$$$;
                    delete doc.data;
                    delete doc._rev;
                    delete doc.items;
                    let item = { ...doc };
                    if (i.doc.$$$)
                        item.$$$ = { ...$$$ };
                    else
                        item.$$$ = {};
                    this.allItems.push(item);
                })
                this.allItems = this.allItems.filter(i => i._id)
                this.formData = this.buildJSONTree(this.allItems);
                this.ftmpComponent.useJsonItem = true;
                this.ftmpComponent.item = this.formData;
                this.templatesList.push('~selected~');
                this.templatesCache['~selected~'] = this.formData;
                this.async(() => {
                    this.selected = this.input.value = '~selected~';
                    this.$update();
                })
            } else if (this.templateId) {
                this.input.value = this.templateId;
                this.loadTemplateToForm(this.templateId);
                this.selected = this.templateId;
            } else {
                this.loadTemplateToForm();
            }
            this.$update();
        }, 10)
    }

    buildJSONTree(allItems) {
        if (!allItems || !allItems.length) return {};
        const itemsMap = {};
        const rootItems = [];
        allItems.forEach(item => itemsMap[item._id] = { ...item, items: [] });
        allItems.forEach(item => {
            if (item.parentId && itemsMap[item.parentId]) {
                itemsMap[item.parentId].items.push(itemsMap[item._id]);
            } else if (item._id.endsWith(':$root-field')) {
                rootItems.push(itemsMap[item._id]);
            }
        })
        const root = rootItems[0];
        return root ? { fields: root } : {};
    }

    async getTemplatesList() {
        if (!this.base?.dbLocal) return [];
        try {
            let result = await this.base.dbLocal.allDocs({
                include_docs: true,
                startkey: 'ftmp-template:',
                endkey: 'ftmp-template:\ufff0'
            })
            result.rows.forEach(row => {
                const name = row.id.replace('ftmp-template:', '');
                this.templatesCache[name] = JSON.parse(row.doc.formData || {});
            })
            const templateNames = result.rows.map(row => row.id.replace('ftmp-template:', ''));
            templateNames.unshift('');
            return templateNames;
        } catch (error) {
            console.error('Error getting templates list:', error);
            return [];
        }
    }
    async getTemplateSrc(name = '', src = '') {
        this.selected = name;
        this.input.value = name || '';
        try {
            if (name && this.templatesCache[name]) {
                this.formData = this.templatesCache[name];
            } else if (name && this.base?.dbLocal) {
                const result = await this.base.dbLocal.get('ftmp-template:' + name);
                this.formData = JSON.parse(result.formData || {});
                this.templatesCache[name] = this.formData;
            } else {
                try {
                    if (!src) {
                        const response = await fetch('/fx/ftmp-template/example-template.json');
                        src = await response.text();
                    }
                    this.formData = JSON.parse(src);
                } catch (error) {
                    console.warn('Could not load example template, using default:', error);
                    this.formData = {
                        fields: {
                            type: 'field',
                            label: 'fields',
                            icon: 'flat-color-icons:services:32',
                            _id: 'field:' + this.base.fxSelected._id + ':$root-field',
                            $$$: {},
                            items: []
                        }
                    }
                }
            }
            this.$update()
        } catch (error) {
            console.error('Error getting template src:', error);
            return '';
        }
    }
    async saveTemplate(e, templateId) {
        templateId ||= this.input.value?.trim();
        if (!this.base?.dbLocal) {
            console.error('Database not available');
            return false;
        }
        if (!templateId) {
            console.error('Template name is required');
            return false;
        }
        try {
            const _id = 'ftmp-template:' + templateId;
            const doc = {
                _id,
                type: 'ftmp-template',
                created: FX.dates().utc,
                updated: FX.dates().utc,
                formData: JSON.stringify(this.formData)
            }
            let existingDoc;
            try {
                existingDoc = await this.base.dbLocal.get(_id);
                doc._rev = existingDoc._rev;
                doc.created = existingDoc.created;
            } catch (error) { }
            new this.base.BS_ITEM({ _id, type: 'ftmp-template', isNew: !existingDoc, doc });
            console.log('Form template saved:', templateId);
            if (!this.templatesList.includes(templateId))
                this.templatesList.push(templateId);
            this.$update();
            this.base?.$update();
            return templateId;
        } catch (error) {
            console.error('Error saving form template:', error);
            return false;
        }
    }
    async loadTemplateToForm(templateId) {
        if (!templateId) {
            await this.getTemplateSrc();
        } else {
            if (this.templatesCache[templateId]) {
                this.formData = this.templatesCache[templateId];
                this.selected = templateId;
                this.input.value = templateId;
            } else {
                await this.getTemplateSrc(templateId);
            }
        }
        const ftmpComponent = this.ftmpComponent;
        if (ftmpComponent && this.formData) {
            ftmpComponent.useJsonItem = true;
            ftmpComponent.item = this.formData;
            ftmpComponent.$update();
        }
        this.$update();
    }
    async deleteTemplate(e, templateId) {
        templateId ||= this.input.value;
        const _id = 'ftmp-template:' + templateId;
        let doc;
        try { doc = await this.base.dbLocal.get(_id) } catch { }
        const bs = new this.base.BS_ITEM({ _id, type: 'ftmp-template', isNew: true, doc: doc || {} });
        bs._deleted = true;
        const index = this.templatesList.indexOf(templateId);
        if (index > -1) this.templatesList.splice(index, 1);
        console.log('Form template deleted:', templateId);
        if (this.selected === templateId) {
            this.selected = '';
            this.input.value = '';
            this.getTemplateSrc();
        }
        this.$update();
        return true;
    }

    createNewTemplate(e, templateId) {
        templateId ||= 'new template';
        this.selected = 'templateId';
        this.input.value = 'templateId';
        this.formData = {
            fields: {
                type: 'field',
                label: 'fields',
                icon: 'flat-color-icons:services:32',
                _id: 'field:' + this.base.fxSelected._id + ':$root-field',
                $$$: {},
                items: []
            }
        }
        this.item = this.formData;
        const ftmpComponent = this.ftmpComponent;
        if (ftmpComponent) {
            ftmpComponent.item = this.formData;
            ftmpComponent.$update();
            this.templatesList.push(templateId);
            this.templatesCache[templateId] = this.formData;
            this.async(() => {
                this.selected = this.input.value = templateId;
                this.$update();
            })
        }
        this.$update();
    }

    regenerateId() {
        if (!this.formData || !this.formData.fields) return;
        const baseId = this.base.fxSelected?._id || 'item';
        const prefix = `field:${baseId}:`;
        const regenerateItem = (item, parentId = null) => {
            if (!item) return item;
            if (item._id && item._id.endsWith(':$root-field')) {
                item._id = `${prefix}$root-field`;
            } else {
                item._id = `${prefix}${FX.ulid()}`;
            }
            item.parentId = parentId;
            if (item.items && Array.isArray(item.items)) {
                item.items.forEach(child => regenerateItem(child, item._id));
            }
            return item;
        }
        regenerateItem(this.formData.fields);
        this.$update();
        console.log('IDs regenerated');
    }

    async applyToSelected() {
        if (!this.base.fxSelected || !this.formData) {
            console.error('fxSelected or formData not available');
            return false;
        }
        try {
            // this.regenerateId();
            const flat = {};
            let root = null;
            const processItem = (item, isRoot = false) => {
                if (!item || !item._id) return;
                const doc = { ...item };
                delete doc.items;
                const props = {
                    id: item._id.split(':').at(-1),
                    parent_id: item.parentId?.split(':').at(-1)
                }
                const bsItem = new this.base.BS_ITEM({
                    _id: item._id,
                    props,
                    type: 'field',
                    isNew: true,
                    doc
                })
                if (isRoot) {
                    root = bsItem;
                } else {
                    flat[item._id] = bsItem;
                }
                if (item.items && Array.isArray(item.items)) {
                    item.items.forEach(child => processItem(child, false));
                }
            }
            processItem(this.formData.fields, true);
            const selectedKey = `field:${this.base.fxSelected._id}`;
            const newTree = await this.base.buildTree(flat, `${selectedKey}:$root-field`, 'field', root);
            if (this.mainFtmp && this.mainFtmp.item) {
                this.mainFtmp.item.fields = newTree;
                this.mainFtmp.$update();
            }
            console.log('Applied to selected with new BS_ITEM instances');
            return true;
        } catch (error) {
            console.error('Error applying to selected:', error);
            return false;
        }
    }

    async exportTemplate(e, templateId) {
        templateId ||= this.input.value.trim();
        templateId ||= this.selected;
        if (!templateId) {
            console.error('No template selected for export');
            return;
        }
        try {
            const dataStr = JSON.stringify(this.formData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `form-template-${templateId}`;
            link.click();
            URL.revokeObjectURL(url);
            console.log('Template exported:', templateId);
        } catch (error) {
            console.error('Error exporting template:', error);
        }
    }
    async importTemplate() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                const reader = new FileReader();
                reader.onload = async (event) => {
                    try {
                        const importedData = JSON.parse(event.target.result);
                        this.formData = importedData;
                        this.item = this.formData;
                        const ftmpComponent = this.$qs('fx-ftmp');
                        if (ftmpComponent) {
                            ftmpComponent.useJsonItem = true;
                            ftmpComponent.item = this.formData;
                            ftmpComponent.$update();
                        }
                        const fileName = this.$qs('input').value || file.name.replace('.json', '').replace('form-template-', '');
                        this.templatesList.add(fileName);
                        this.selected = fileName;
                        this.$update();
                        console.log('Template imported successfully');
                    } catch (error) {
                        console.error('Error parsing imported file:', error);
                    }
                }
                reader.readAsText(file);
            }
            input.click();
        } catch (error) {
            console.error('Error importing template:', error);
        }
    }

    static styles = [$styles]

    render() {
        return html`
            <div class="horizontal flex w100 h100 relative box overflow-h bral">
                <div class="vertical w100 h100 relative overflow-h" style="min-width: 200px; width: 200px; height: calc(100vh - 80px)">
                    <div class="vertical w100 h100 relative overflow-y">
                        ${this.templatesList.map(name => html`
                            <label class="horizontal align w100 h24 brbl p4 pointer hover"
                                   ?selected=${this.selected === name}
                                   @click=${() => this.loadTemplateToForm(name)}
                                   style="${this.selected === name ? 'background: var(--fx-color-selected); color: white;' : ''}"
                            >${name || 'demo template'}</label>
                        `)}
                        <div class="flex"></div>
                    </div>
                    <div class="horizontal w100">
                        <fx-icon class="m4" url="carbon:menu" scale=1 an="btn" @click=${e => { this.showMenu = !this.showMenu; this.$update(); }}></fx-icon>
                        <input class="inp fm w100 h32 bral p8 mr4 box flex" placeholder="template name">
                    </div>
                    <div class="horizontal flex">
                        <div class="menu-container relative">
                            ${this.showMenu ? html`
                                <div class="fs menu-dropdown absolute" @click=${(e) => e.stopPropagation()}>
                                    <fx-icon class="m2" url="carbon:add" scale=1 an="btn" @click=${this.createNewTemplate} txt="New Template"></fx-icon>
                                    <fx-icon class="m2" url="carbon:save" scale=1 an="btn" @click=${this.saveTemplate} txt="Save"></fx-icon>
                                    <fx-icon class="m2" url="carbon:trash-can" scale=1 an="btn" @click=${this.deleteTemplate} txt="Delete"></fx-icon>
                                    <div style="border-top: 1px solid #ddd; margin: 8px 0; padding-top: 8px;">
                                        <fx-icon class="m2" url="solar:refresh-linear" scale=1 an="btn" @click=${this.regenerateId} txt="Regenerate ID"></fx-icon>
                                        <fx-icon class="m2" url="carbon:checkbox-checked" scale=1 an="btn" @click=${this.applyToSelected} txt="Apply to selected"></fx-icon>
                                    </div>
                                    <div style="border-top: 1px solid #ddd; margin: 8px 0; padding-top: 8px;">
                                        <fx-icon class="m2" url="icons8:export" scale=1 an="btn" @click=${this.exportTemplate} txt="Export"></fx-icon>
                                        <fx-icon class="m2" url="icons8:import" scale=1 an="btn" @click=${this.importTemplate} txt="Import"></fx-icon>
                                    </div>
                                </div>
                            ` : html``}
                        </div>
                    </div>
                </div>
                <fx-splitter color="gray" vertical size="1"></fx-splitter>
                <div class="vertical w100 relative box overflow-h">
                    <fx-ftmp class="flex w100 h100"
                             ?showConstructor=${true}
                             @change=${this.onSrcChange}
                             .base=${this.base}
                             ></fx-ftmp>
                </div>
            </div>
        `
    }
}

customElements.define('fx-ftmp-template', FxFtmpTemplate);
