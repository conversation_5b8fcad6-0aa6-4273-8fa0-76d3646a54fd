import { FxElement, html } from '/fx.js';
import { $styles } from './ftmp-template.x.js';
import '../button/button.js';
import '../splitter/splitter.js';
import '../ftmp/ftmp.js';

export class FxFtmpTemplate extends FxElement {
    static properties = {
        base: { type: Object },
        data: { type: Object },
        templatesList: { type: Array, default: [] },
        selected: { type: String, default: '' },
        item: { type: Object },
        templateId: { type: String, default: '' }
    }

    async firstUpdated() {
        super.firstUpdated();
        this.base = FX.base || {};
        this.templatesList = await this.getTemplatesList();
        await this.getTemplateSrc();
        this.async(() => {
            if (this.templateId) {
                this.getTemplateSrc(this.templateId);
                this.selected = this.templateId;
            } else {
                //this.getTemplateSrc();
            }
        })
    }

    async getTemplatesList() {
        if (!this.base?.dbLocal) return [];
        try {
            let result = await this.base.dbLocal.allDocs({
                include_docs: true,
                startkey: 'ftmp-template:',
                endkey: 'ftmp-template:\ufff0'
            })
            result = result.rows.map(row => row.id.replace('ftmp-template:', ''));
            result.unshift('');
            return result;
        } catch (error) {
            console.error('Error getting templates list:', error);
            return [];
        }
    }

    async getTemplateSrc(name = '', src = '') {
        this.selected = name;
        this.$qs('input').value = name || '';
        if (!this.base?.dbLocal) return '';
        try {
            let result;
            if (name)
                result = await this.base.dbLocal.get('ftmp-template:' + name);
            if (result) {

            } else {
                try {
                    if (!src) {
                        const response = await fetch('/fx/ftmp-template/example-template.json');
                        src = await response.text();
                    }
                } catch (error) {
                    console.warn('Could not load example template, using default:', error);
                    src = ``;
                }
            }
            this.item = JSON.parse(src);
            this.$update()
        } catch (error) {
            console.error('Error getting template src:', error);
            return '';
        }
    }
    async saveTemplate(e, templateId) {
        templateId ||= this.$qs('input').value;
        if (!this.base?.dbLocal || !templateId) return false;
        try {
            const _id = 'table-template:' + templateId;
            const doc = {
                _id,
                type: 'table-template',
                // tableId: this.id,
                mode: this.mode,
                created: FX.dates().utc,
                columns: this.serializeWithFunctions(this.data.columns || []),
                footerCalculations: this.serializeWithFunctions(this.data.footerCalculations || {}),
                options: this.serializeWithFunctions(this.data.options || {})
            }
            const bs = new this.base.BS_ITEM({ _id, type: 'table-template', isNew: true, doc });
            console.log('Template saved:', templateId);
            this.templatesList.add(templateId);
            this.$update();
            this.base?.$update();
            return templateId;
        } catch (error) {
            console.error('Error saving template:', error);
            return false;
        }
    }
    async deleteTemplate(e, templateId) {
        templateId ||= this.$qs('input').value;
        if (!this.base?.dbLocal || !templateId) {
            return false;
        }
        try {
            const _id = 'table-template:' + templateId;
            const doc = await this.base.dbLocal.get(_id);
            const bs = new this.base.BS_ITEM({ _id, type: 'table-template', isNew: true, doc });
            bs._deleted = true;
            console.log('Template deleted:', templateId);
            this.templatesList.remove(templateId);
            this.$update();
            this.base?.$update();
            return true;
        } catch (error) {
            console.error('Error deleting template:', error);
            return false;
        }
    }

    onSrcChange(e) {
        console.log(e);
    }

    static styles = [$styles]

    render() {
        return html`
            <div class="horizontal flex w100 h100 relative box overflow-h bral">
                <div class="vertical w100 h100 relative overflow-h" style="min-width: 160px; width: 160px; height: calc(100vh - 80px)">
                    <div class="vertical w100 h100 relative overflow-y">
                        ${this.templatesList.map(name => html`
                            <label class="w100 h28 brbl p4 pointer" ?selected=${this.selected === name} @click=${() => this.getTemplateSrc(name)}>${name || 'demo template'}</label>
                        `)}
                        <div class="flex"></div>
                    </div>
                    <input class="inp fm w100 h28 brtl brbl p4 box" placeholder="new template name">
                    <div class="vertical w100 no-flex p4">
                        <fx-icon class="mb10 mt2" url="carbon:save" scale=1 an="btn" br="square" @click=${this.saveTemplate} txt="Save new"></fx-icon>
                        <fx-icon url="carbon:trash-can" scale=1 an="btn" br="square" @click=${this.deleteTemplate} txt="Delete selected"></fx-icon>
                    </div>
                </div>
                <fx-splitter color="gray" vertical size="2"></fx-splitter>
                <div class="vertical w100 relative box overflow-h">
                    <fx-ftmp class="flex w100 h100" ?showConstructor=${true} @change=${this.onSrcChange} .base=${this.base}></fx-ftmp>
                </div>
            </div>
        `
    }
}

customElements.define('fx-ftmp-template', FxFtmpTemplate);
