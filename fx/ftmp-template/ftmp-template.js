import { FxElement, html } from '/fx.js';
import { $styles } from './ftmp-template.x.js';
import '../button/button.js';
import '../splitter/splitter.js';
import '../ftmp/ftmp.js';

export class FxFtmpTemplate extends FxElement {
    static properties = {
        base: { type: Object },
        data: { type: Object },
        templatesList: { type: Array, default: [] },
        templatesCache: { type: Object, default: {} },
        selected: { type: String, default: '' },
        item: { type: Object },
        templateId: { type: String, default: '' },
        formData: { type: Object },
        mainFtmp: { type: Object },
        flatItems: { type: Object, default: {} },
        allItems: { type: Array, default: [] }
    }

    async firstUpdated() {
        super.firstUpdated();
        this.base = FX.base || {};
        this.templatesCache = {};
        this.templatesList = await this.getTemplatesList();

        this.async(() => {
            // Получаем данные от mainFtmp если доступны
            if (this.mainFtmp?.item) {
                this.formData = this.convertItemToJSON(this.mainFtmp.item);
                console.log('Loaded from mainFtmp:', this.formData);
            } else if (this.templateId) {
                this.loadTemplateToForm(this.templateId);
                this.selected = this.templateId;
            } else {
                this.loadTemplateToForm();
            }
            this.$update();
        })
    }

    // Обработчик изменений в компоненте ftmp
    'item-changed'(e) {
        if (e.detail && e.detail.item) {
            this.formData = e.detail.item;
        }
    }

    // Преобразование item в JSON объект
    convertItemToJSON(item) {
        if (!item) return {};

        // Создаем глубокую копию и очищаем от служебных свойств
        const cleanItem = (obj) => {
            if (!obj || typeof obj !== 'object') return obj;

            const cleaned = {};
            Object.keys(obj).forEach(key => {
                if (key.startsWith('_') && key !== '_id') return; // Пропускаем служебные поля кроме _id
                if (key === 'doc' || key === 'items' || key === 'expanded' || key === 'selected') {
                    if (key === 'items' && Array.isArray(obj[key])) {
                        cleaned[key] = obj[key].map(item => cleanItem(item));
                    } else if (key !== 'doc' && key !== 'expanded' && key !== 'selected') {
                        cleaned[key] = cleanItem(obj[key]);
                    }
                } else {
                    cleaned[key] = cleanItem(obj[key]);
                }
            });
            return cleaned;
        };

        return cleanItem(item);
    }

    // Преобразование JSON в flat структуру для buildTree
    convertJSONToFlat(jsonData) {
        const flat = {};
        let root = null;

        const processItem = (item, parentId = null) => {
            if (!item || !item._id) return;

            const _id = item._id;
            const doc = { ...item };
            delete doc.items; // Удаляем items из doc

            const props = {
                id: _id.split(':').at(-1),
                parent_id: parentId?.split(':').at(-1)
            };

            const bsItem = new this.base.BS_ITEM({
                _id,
                props,
                type: 'field',
                isLoad: true,
                doc
            });

            if (_id.endsWith(':$root-field')) {
                root = bsItem;
            } else {
                flat[_id] = bsItem;
            }

            // Обрабатываем дочерние элементы
            if (item.items && Array.isArray(item.items)) {
                item.items.forEach(childItem => {
                    processItem(childItem, _id);
                });
            }
        };

        if (jsonData.fields) {
            processItem(jsonData.fields);
        } else if (jsonData._id) {
            processItem(jsonData);
        }

        return { flat, root };
    }

    async getTemplatesList() {
        if (!this.base?.dbLocal) return [];
        try {
            let result = await this.base.dbLocal.allDocs({
                include_docs: true,
                startkey: 'ftmp-template:',
                endkey: 'ftmp-template:\ufff0'
            })

            // Кешируем данные шаблонов
            result.rows.forEach(row => {
                const name = row.id.replace('ftmp-template:', '');
                this.templatesCache[name] = row.doc.formData || {};
            });

            const templateNames = result.rows.map(row => row.id.replace('ftmp-template:', ''));
            templateNames.unshift(''); // Добавляем пустой элемент для демо
            return templateNames;
        } catch (error) {
            console.error('Error getting templates list:', error);
            return [];
        }
    }

    async getTemplateSrc(name = '', src = '') {
        this.selected = name;
        this.$qs('input').value = name || '';

        try {
            if (name && this.templatesCache[name]) {
                // Используем кешированные данные
                this.formData = this.templatesCache[name];
            } else if (name && this.base?.dbLocal) {
                // Загружаем из базы данных
                const result = await this.base.dbLocal.get('ftmp-template:' + name);
                this.formData = result.formData || {};
                this.templatesCache[name] = this.formData; // Кешируем
            } else {
                // Загружаем пример шаблона
                try {
                    if (!src) {
                        const response = await fetch('/fx/ftmp-template/example-template.json');
                        src = await response.text();
                    }
                    this.formData = JSON.parse(src);
                } catch (error) {
                    console.warn('Could not load example template, using default:', error);
                    this.formData = {
                        fields: {
                            _id: 'field:new:$root-field',
                            type: 'field',
                            label: 'New Form',
                            is: 'row',
                            $$$: { label0: 'New Form Template' },
                            items: []
                        }
                    };
                }
            }

            this.item = this.formData;
            this.$update()
        } catch (error) {
            console.error('Error getting template src:', error);
            return '';
        }
    }
    async saveTemplate(templateId) {
        templateId ||= this.$qs('input').value?.trim();
        if (!this.base?.dbLocal) {
            console.error('Database not available');
            return false;
        }
        if (!templateId) {
            console.error('Template name is required');
            return false;
        }
        try {
            // Получаем данные формы из компонента fx-ftmp
            const ftmpComponent = this.$qs('fx-ftmp');
            if (ftmpComponent && ftmpComponent.item) {
                this.formData = this.convertItemToJSON(ftmpComponent.item);
            }

            // Обновляем кеш
            this.templatesCache[templateId] = this.formData;

            const _id = 'ftmp-template:' + templateId;
            const doc = {
                _id,
                type: 'ftmp-template',
                created: FX.dates().utc,
                updated: FX.dates().utc,
                formData: this.formData
            }

            // Проверяем, существует ли уже такой шаблон
            let existingDoc;
            try {
                existingDoc = await this.base.dbLocal.get(_id);
                doc._rev = existingDoc._rev;
                doc.created = existingDoc.created; // Сохраняем дату создания
            } catch (error) {
                // Документ не существует, создаем новый
            }

            new this.base.BS_ITEM({ _id, type: 'ftmp-template', isNew: !existingDoc, doc });
            console.log('Form template saved:', templateId);

            // Обновляем список шаблонов
            if (!this.templatesList.includes(templateId)) {
                this.templatesList.push(templateId);
            }

            this.$update();
            this.base?.$update();
            return templateId;
        } catch (error) {
            console.error('Error saving form template:', error);
            return false;
        }
    }
    async deleteTemplate(templateId) {
        templateId ||= this.$qs('input').value;
        if (!this.base?.dbLocal || !templateId) {
            return false;
        }
        try {
            const _id = 'ftmp-template:' + templateId;
            const doc = await this.base.dbLocal.get(_id);
            const bs = new this.base.BS_ITEM({ _id, type: 'ftmp-template', isNew: false, doc });
            bs._deleted = true;
            console.log('Form template deleted:', templateId);

            // Удаляем из кеша
            delete this.templatesCache[templateId];

            // Удаляем из списка шаблонов
            const index = this.templatesList.indexOf(templateId);
            if (index > -1) {
                this.templatesList.splice(index, 1);
            }

            // Очищаем выбранный шаблон если он был удален
            if (this.selected === templateId) {
                this.selected = '';
                this.$qs('input').value = '';
                this.getTemplateSrc(); // Загружаем пример
            }

            this.$update();
            this.base?.$update();
            return true;
        } catch (error) {
            console.error('Error deleting form template:', error);
            return false;
        }
    }

    onSrcChange() {
        // Обновляем данные формы при изменениях
        const ftmpComponent = this.$qs('fx-ftmp');
        if (ftmpComponent && ftmpComponent.item) {
            this.formData = ftmpComponent.item;
        }
        console.log('Form data changed:', this.formData);
    }

    // Метод для загрузки шаблона в форму
    async loadTemplateToForm(templateId) {
        if (!templateId) {
            // Загружаем пример шаблона
            await this.getTemplateSrc();
        } else {
            // Используем кешированные данные если доступны
            if (this.templatesCache[templateId]) {
                this.formData = this.templatesCache[templateId];
                this.selected = templateId;
                this.$qs('input').value = templateId;
            } else {
                await this.getTemplateSrc(templateId);
            }
        }

        const ftmpComponent = this.$qs('fx-ftmp');
        if (ftmpComponent && this.formData) {
            ftmpComponent.useJsonItem = true;
            ftmpComponent.item = this.formData;
            ftmpComponent.$update();
        }
        this.$update();
    }

    // Метод для создания нового шаблона
    createNewTemplate() {
        this.selected = '';
        this.$qs('input').value = '';

        // Создаем новый шаблон на основе текущих данных mainFtmp или пустой
        if (this.mainFtmp?.item) {
            this.formData = this.convertItemToJSON(this.mainFtmp.item);
        } else {
            this.formData = {
                fields: {
                    _id: 'field:new:$root-field',
                    type: 'field',
                    label: 'New Form',
                    is: 'row',
                    $$$: {
                        label0: 'New Form Template',
                        class: 'p8',
                        style: 'background: #f9f9f9; border-radius: 4px;'
                    },
                    items: [
                        {
                            _id: 'field:new:sample',
                            type: 'field',
                            label: 'Sample Field',
                            is: 'input',
                            isType: 'text',
                            $$$: {
                                fIcon: 'carbon:text-font',
                                fIconSize: 18
                            }
                        }
                    ]
                }
            };
        }

        this.item = this.formData;
        const ftmpComponent = this.$qs('fx-ftmp');
        if (ftmpComponent) {
            ftmpComponent.item = this.formData;
            ftmpComponent.$update();
        }
        this.$update();
    }

    // Применение шаблона к mainFtmp
    async applyTemplate() {
        if (!this.mainFtmp || !this.formData) {
            console.error('mainFtmp or formData not available');
            return false;
        }

        try {
            // Получаем текущие данные из редактора
            const ftmpComponent = this.$qs('fx-ftmp');
            if (ftmpComponent && ftmpComponent.item) {
                this.formData = this.convertItemToJSON(ftmpComponent.item);
            }

            // Преобразуем JSON в flat структуру
            const selectedKey = 'field:' + (this.base.fxSelected?._id || 'applied');
            const { flat, root } = this.convertJSONToFlat(this.formData);

            // Строим новое дерево
            const newTree = await this.base.buildTree(flat, selectedKey + ':$root-field', 'field', root);

            // Применяем к mainFtmp
            if (this.mainFtmp.item) {
                this.mainFtmp.item.fields = newTree;
                this.mainFtmp.$update();
            }

            console.log('Template applied to mainFtmp');
            return true;
        } catch (error) {
            console.error('Error applying template:', error);
            return false;
        }
    }

    // Экспорт шаблона в JSON файл
    async exportTemplate(templateId) {
        templateId ||= this.$qs('input').value.trim();
        templateId ||= this.selected;
        if (!templateId) {
            console.error('No template selected for export');
            return;
        }

        try {
            await this.getTemplateSrc(templateId);
            const dataStr = JSON.stringify(this.formData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `form-template-${templateId}`;
            link.click();
            URL.revokeObjectURL(url);
            console.log('Template exported:', templateId);
        } catch (error) {
            console.error('Error exporting template:', error);
        }
    }

    // Импорт шаблона из JSON файла
    async importTemplate() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = async (event) => {
                    try {
                        const importedData = JSON.parse(event.target.result);
                        this.formData = importedData;
                        this.item = this.formData;

                        const ftmpComponent = this.$qs('fx-ftmp');
                        if (ftmpComponent) {
                            ftmpComponent.useJsonItem = true;
                            ftmpComponent.item = this.formData;
                            ftmpComponent.$update();
                        }

                        // Предлагаем имя для нового шаблона
                        const fileName = this.$qs('input').value || file.name.replace('.json', '').replace('form-template-', '');
                        this.templatesList.add(fileName);
                        this.selected = fileName;
                        this.$update();

                        console.log('Template imported successfully');
                    } catch (error) {
                        console.error('Error parsing imported file:', error);
                    }
                };
                reader.readAsText(file);
            };
            input.click();
        } catch (error) {
            console.error('Error importing template:', error);
        }
    }

    static styles = [$styles]

    render() {
        return html`
            <div class="horizontal flex w100 h100 relative box overflow-h bral">
                <div class="vertical w100 h100 relative overflow-h" style="min-width: 200px; width: 200px; height: calc(100vh - 80px)">
                    <div class="vertical w100 h100 relative overflow-y">
                        ${this.templatesList.map(name => html`
                            <label class="w100 h32 brbl p8 pointer hover"
                                   ?selected=${this.selected === name}
                                   @click=${() => this.loadTemplateToForm(name)}
                                   style="border-bottom: 1px solid #eee; ${this.selected === name ? 'background: var(--fx-color-selected); color: white;' : ''}"
                            >${name || 'demo template'}</label>
                        `)}
                        <div class="flex"></div>
                    </div>
                    <input class="inp fm w100 h32 brtl brbl p8 box" placeholder="template name" style="border-top: 1px solid #ddd;">
                    <div class="vertical w100 no-flex p8" style="border-top: 1px solid #ddd;">
                        <fx-icon class="mb8 mt2" url="carbon:add" scale=1 an="btn" br="square" @click=${this.createNewTemplate} txt="New Template"></fx-icon>
                        <fx-icon class="mb8" url="carbon:save" scale=1 an="btn" br="square" @click=${this.saveTemplate} txt="Save Template"></fx-icon>
                        <fx-icon class="mb8" url="carbon:trash-can" scale=1 an="btn" br="square" @click=${this.deleteTemplate} txt="Delete Template"></fx-icon>
                        <div style="border-top: 1px solid #ddd; margin: 8px 0; padding-top: 8px;">
                            <fx-icon class="mb8" url="carbon:checkmark" scale=1 an="btn" br="square" @click=${this.applyTemplate} txt="Apply Template" style="background: #28a745; color: white;"></fx-icon>
                            <fx-icon class="mb8" url="carbon:export" scale=1 an="btn" br="square" @click=${this.exportTemplate} txt="Export Template"></fx-icon>
                            <fx-icon url="carbon:import" scale=1 an="btn" br="square" @click=${this.importTemplate} txt="Import Template"></fx-icon>
                        </div>
                    </div>
                </div>
                <fx-splitter color="gray" vertical size="2"></fx-splitter>
                <div class="vertical w100 relative box overflow-h">
                    <fx-ftmp class="flex w100 h100"
                             ?showConstructor=${true}
                             @change=${this.onSrcChange}
                             .base=${this.base}
                             ></fx-ftmp>
                </div>
            </div>
        `
    }
}

customElements.define('fx-ftmp-template', FxFtmpTemplate);
