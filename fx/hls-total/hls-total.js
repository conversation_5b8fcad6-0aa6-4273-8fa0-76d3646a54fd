import { FxElement, html, css } from '/fx.js';

import '../table/table.js';
import '../icon/icon.js';

customElements.define('fx-hls-total', class FxHlsTotal extends FxElement {
    static properties = {
        id: { type: String, default: 'fx-hls-total' },
        base: { type: Object },
        mode: { type: String, default: 'water', save: true },
        item: { type: Object, notify: true },
        _item: { type: Object },
        data: { type: Object },
    }
    get _id() { return 'fx-hls-total-' + this.mode }
    get table() { return this.$qs('#' + this._id) }
    get modeR() { return { water: 'Вода', meal: 'Питание', measure: 'Замеры', sport: 'Спорт', walk: 'Ходьба', sleep: 'Сон', misc: 'Развлечения' }[this.mode] }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(async () => {
            await this.getData();
            await this._init();
        }, 100)
    }

    async getData() {
        let children = BS_UTILS.allItems(this.base.fxSelected).filter(i => i.is === 'hlsDay'),
            keys = children.map(i => 'hlsDay:' + i._id),
            allDocs = await this.base.dbLocal.allDocs({ keys, include_docs: true }),
            item = { water: [], meal: [], measure: [], sport: [], walk: [], sleep: [], misc: [] };
        (allDocs || []).rows.map(i => {
            (i.doc?.water || []).map(o => { if (!o._deleted) item.water.push({ dateTime: i.doc.date + 'T' + o.time, date: i.doc.date, ...o }) });
            item.water = FX.sortBy(item.water, 'dateTime');
            (i.doc?.meal || []).map(o => { if (!o._deleted) item.meal.push({ dateTime: i.doc.date + 'T' + o.time, date: i.doc.date, ...o }) });
            item.meal = FX.sortBy(item.meal, 'dateTime');
            (i.doc?.measure || []).map(o => { if (!o._deleted) item.measure.push({ dateTime: i.doc.date + 'T' + o.time, date: i.doc.date, ...o }) });
            item.measure = FX.sortBy(item.measure, 'dateTime');
            (i.doc?.sport || []).map(o => { if (!o._deleted) item.sport.push({ dateTime: i.doc.date + 'T' + o.time, date: i.doc.date, ...o }) });
            item.sport = FX.sortBy(item.sport, 'dateTime');
            (i.doc?.walk || []).map(o => { if (!o._deleted) item.walk.push({ dateTime: i.doc.date + 'T' + o.time, date: i.doc.date, ...o }) });
            item.walk = FX.sortBy(item.walk, 'dateTime');
            (i.doc?.sleep || []).map(o => { if (!o._deleted) item.sleep.push({ dateTime: i.doc.date + 'T' + (o.time || o.time0), date: i.doc.date, ...o }) });
            item.sleep = FX.sortBy(item.sleep, 'dateTime');
            (i.doc?.misc || []).map(o => { if (!o._deleted) item.misc.push({ dateTime: i.doc.date + 'T' + (o.time || o.time0), date: i.doc.date, ...o }) });
            item.misc = FX.sortBy(item.misc, 'dateTime');
        })
        this._item = item;
    }
    async 'item-changed'(e) {
        await this.getData();
        this.async(async () => {
            this.data = undefined;
            this.table.updateVisibleRows();
            await this._init();
        })
    }
    async modeSet(m) {
        this.mode = m;
        this.async(async () => {
            this.data = undefined;
            this.table.updateVisibleRows();
            await this._init();
        })
    }
    async _init() {
        this.table.selectedRowIndex = -1;
        this.options = { rowHeight: 36, headerStyle: 'font-size: 14px;', typeColumn: 'input', footerStyle: 'font-size: 14px;' };
        this['table' + this.mode]();
        this.$update();
        this.async(() => {
            this.table.updateVisibleRows();
        }, 50)
    }

    tablewater() {
        this.data = this._item?.water || [];
        this.columns = [
            { field: '$idx', header: '№', width: 50 },
            { header: 'Дата', field: 'date', width: 100, fontSize: '14px' },
            { header: 'Время', field: 'time', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Кол-во (л)', field: 'sum', width: 72, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: ' ', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 300, width: 'auto', style: 'font-size: 16px; text-wrap: auto; height: 36px;' },
        ]
        this.calc = { $idx: { type: 'count' }, sum: { type: 'sum', decimals: 2 } };
    }
    tablemeal() {
        this.data = this._item?.meal || [];
        this.columns = [
            { field: '$idx', header: '№', width: 50 },
            { header: 'Дата', field: 'date', width: 100, fontSize: '14px' },
            { header: 'Время', field: 'time', typeColumn: 'input', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Тип приема', field: 'type', width: 91, typeColumn: 'select', options: ['', 'завтрак', 'обед', 'ужин', 'перекус'] },
            { field: 'name', header: 'Название блюда', showTitle: true, typeColumn: 'textarea', minWidth: 300, width: 'auto', style: 'font-size: 14px; text-wrap: auto; height: 48px;' },
            { header: 'Кол. (г)', field: 'sum', width: 69, typeColumn: 'input', textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Калории', field: 'cal2', width: 69, typeColumn: 'span', calc: (e) => (+e.cal) / 100 * +e.sum, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Белки', field: 'prot2', width: 69, typeColumn: 'span', calc: (e) => (+e.prot) / 100 * +e.sum, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Жиры', field: 'fat2', width: 69, typeColumn: 'span', calc: (e) => (+e.fat) / 100 * +e.sum, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Углеводы', field: 'carb2', width: 69, typeColumn: 'span', calc: (e) => (+e.carb) / 100 * +e.sum, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', width: 360, style: 'font-size: 14px; text-wrap: auto; height: 48px;' },
        ]
        this.options.rowHeight = 54;
        this.calc = { $idx: { type: 'count' }, sum: { type: 'sum', decimals: 2 }, cal2: { type: 'sum', decimals: 2 }, prot2: { type: 'sum', decimals: 2 }, fat2: { type: 'sum', decimals: 2 }, carb2: { type: 'sum', decimals: 2 } };
    }
    tablemeasure() {
        this.data = this._item?.measure || [];
        this.columns = [
            { field: '$idx', header: '№', width: 50 },
            { header: 'Дата', field: 'date', width: 100, fontSize: '14px' },
            { header: 'Время', field: 'time', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Вес', field: 'ves', width: 60 },
            { header: 'ОБ', field: 'ob', width: 60 },
            { header: 'ОЯ', field: 'oy', width: 60 },
            { header: 'ОТ', field: 'ot', width: 60 },
            { header: 'ОЖ', field: 'oj', width: 60 },
            { header: 'ОГ', field: 'og', width: 60 },
            { header: 'ОР', field: 'or', width: 60 },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 300, width: 'auto', style: 'font-size: 16px; text-wrap: auto; height: 36px;' },
        ]
        this.calc = { $idx: { type: 'count' } };
    }
    tablesport() {
        this.data = this._item?.sport || [];
        this.columns = [
            { field: '$idx', header: '№', width: 50 },
            { header: 'Дата', field: 'date', width: 100, fontSize: '14px' },
            { header: 'Время', field: 'time', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Длительность (ч)', field: 'dlit', width: 120, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Название', field: 'name', width: 160, textAlign: 'left', alignItems: 'flex-start' },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 300, width: 'auto', style: 'font-size: 16px; text-wrap: auto; height: 36px;' },
        ]
        this.calc = { $idx: { type: 'count' }, dlit: { type: 'sum', decimals: 2 } };
    }
    tablewalk() {
        this.data = this._item?.walk || [];
        this.columns = [
            { field: '$idx', header: '№', width: 50 },
            { header: 'Дата', field: 'date', width: 100, fontSize: '14px' },
            { header: 'Время', field: 'time', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Длительность (ч)', field: 'dlit', width: 120, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Кол. шагов (x1000)', field: 'step', width: 132, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Дистанция (км)', field: 'dist', width: 120, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { header: 'Этажи', field: 'floor', width: 80, textAlign: 'right', footerStyle: 'justify-content: end;' },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 300, width: 'auto', style: 'font-size: 16px; text-wrap: auto; height: 36px;' },
        ]
        this.calc = { $idx: { type: 'count' }, dlit: { type: 'sum', decimals: 2 }, step: { type: 'sum', decimals: 2 }, dist: { type: 'sum', decimals: 2 }, floor: { type: 'sum', decimals: 2 } };
    }
    calcDlit(e) {
        if (!e.time0 || !e.time1) return '';
        let res = '';
        try {
            let t0 = '2024-01-01T' + e.time0,
                t1 = e.time0.split(':')[0] <= e.time1.split(':')[0] ? '2024-01-01T' + e.time1 : '2024-01-02T' + e.time1;
            res = ((new Date(t1)).getTime() - (new Date(t0)).getTime()) / 1000 / 60 / 60;
        } catch (error) { }
        return parseFloat(res);
    }
    tablesleep() {
        this.data = this._item.sleep || [];
        this.columns = [
            { field: '$idx', header: '№', width: 50 },
            { header: 'Дата', field: 'date', width: 100, fontSize: '14px' },
            { header: 'Время c', field: 'time0', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Время по', field: 'time1', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Длительность (ч)', field: 'dlit', width: 120, textAlign: 'right', footerStyle: 'justify-content: end;', typeColumn: 'span', calc: this.calcDlit },
            { header: 'Качество', field: 'rating', typeColumn: 'rating', width: 120 },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 300, width: 'auto', style: 'font-size: 16px; text-wrap: auto; height: 36px;' },
        ]
        this.calc = { $idx: { type: 'count' }, dlit: { type: 'sum', decimals: 2 } };
    }
    tablemisc() {
        this.data = this._item.misc || [];
        this.columns = [
            { field: '$idx', header: '№', width: 50 },
            { header: 'Дата', field: 'date', width: 100, fontSize: '14px' },
            { header: 'Время c', field: 'time0', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Время по', field: 'time1', typeInput: 'time', width: 72, textAlign: 'center', class: 'fs' },
            { header: 'Длит. (ч)', field: 'dlit', width: 66, textAlign: 'right', footerStyle: 'justify-content: end;', typeColumn: 'span', calc: this.calcDlit },
            { header: 'Мероприятие', field: 'name', typeColumn: 'textarea', width: 200, style: 'font-size: 14px; text-wrap: auto; height: 48px;' },
            { header: 'Моя оценка', field: 'rating', typeColumn: 'rating', width: 200, args: { length: 10 }, textAlign: 'center' },
            { field: 'info', header: 'Inf', width: 32, typeColumn: 'info' },
            { header: 'Примечание', field: 'note', showTitle: true, typeColumn: 'textarea', minWidth: 300, width: 'auto', style: 'font-size: 14px; text-wrap: auto; height: 48px;' },
        ]
        this.options.rowHeight = 54;
        this.calc = { $idx: { type: 'count' }, dlit: { type: 'sum', decimals: 2 } };
    }
    render() {
        return html`
            <div class="main vertical flex w100 h100 relative">
            <div class="horizontal w100 center wrap">
            <fx-icon class="pointer m4 ${this.mode === 'water' ? 'o10' : 'o3'}" icon="gm-water-bottle" fill="#2196F3" size="32" an="btn" br="circle" back="lightyellow" @click=${e => this.modeSet('water')}></fx-icon>
                    <div style="height: 4px; width: 4px; border-radius: 50%; background: gray; margin-top: 24px; margin-left: -4px;" ?hidden=${!this._item?.water?.length}></div>
                    <fx-icon class="pointer m4 ${this.mode === 'meal' ? 'o10' : 'o3'}" icon="gm-meal" fill="#4CAF50" size="32" an="btn" br="circle" back="lightyellow" @click=${e => this.modeSet('meal')}></fx-icon>
                    <div style="height: 4px; width: 4px; border-radius: 50%; background: gray; margin-top: 24px; margin-left: -4px;" ?hidden=${!this._item?.meal?.length}></div>
                    <fx-icon class="pointer m4 ${this.mode === 'measure' ? 'o10' : 'o3'}" icon="gm-measure-tape" fill="orange" size="32" an="btn" br="circle" back="lightyellow" @click=${e => this.modeSet('measure')}></fx-icon>
                    <div style="height: 4px; width: 4px; border-radius: 50%; background: gray; margin-top: 24px; margin-left: -4px;" ?hidden=${!this._item?.measure?.length}></div>
                    <fx-icon class="pointer m4 ${this.mode === 'sport' ? 'o10' : 'o3'}" icon="bx-i-run" fill="red" size="32" an="btn" br="circle" back="lightyellow" @click=${e => this.modeSet('sport')}></fx-icon>
                    <div style="height: 4px; width: 4px; border-radius: 50%; background: gray; margin-top: 24px; margin-left: -4px;" ?hidden=${!this._item?.sport?.length}></div>
                    <fx-icon class="pointer m4 ${this.mode === 'walk' ? 'o10' : 'o3'}" icon="gm-walk" fill="#9370DB" size="32" an="btn" br="circle" back="lightyellow" @click=${e => this.modeSet('walk')}></fx-icon>
                    <div style="height: 4px; width: 4px; border-radius: 50%; background: gray; margin-top: 24px; margin-left: -4px;" ?hidden=${!this._item?.walk?.length}></div>
                    <fx-icon class="pointer m4 ${this.mode === 'sleep' ? 'o10' : 'o3'}" icon="fa-s-bed" fill="#FF69B4" size="32" an="btn" br="circle" back="lightyellow" @click=${e => this.modeSet('sleep')}></fx-icon>
                    <div style="height: 4px; width: 4px; border-radius: 50%; background: gray; margin-top: 24px; margin-left: -4px;" ?hidden=${!this._item?.sleep?.length}></div>
                    <fx-icon class="pointer m4 ${this.mode === 'misc' ? 'o10' : 'o3'}" icon="carbon:theater" fill="#a110cd" size="32" an="btn" br="circle" back="lightyellow" @click=${e => this.modeSet('misc')}></fx-icon>
                    <div style="height: 4px; width: 4px; border-radius: 50%; background: gray; margin-top: 24px; margin-left: -4px;" ?hidden=${!this._item?.misc?.length}></div>
                    <div class="flex"></div>
                    <!-- <fx-icon class="pointer m4" icon="fc-combo_chart" size="32" an="btn" br="circle" back="lightyellow" style="margin-left: auto"></fx-icon> -->
                </div>
                <div class="vertical w100 h100 relative overflow-h">
                    <fx-table id=${this._id} rowHeight=${this.mode === 'misc' || this.mode === 'meal' ? 48 : 36} style="min-height: ${this.mode === 'meal' ? '50%' : '100%'};"  topLabel=${this.modeR} hide="nc" .options=${this.options} .columns=${this.columns} .data=${this.data} .footerCalculations=${this.calc} mode=${this.modeR} .base=${this.base}></fx-table>
                </div>
            </div>
        `
    }
})

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons =
{
    "fc-combo_chart": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <g fill=\"#00BCD4\">\r\n        <rect x=\"37\" y=\"18\" width=\"6\" height=\"24\"/>\r\n        <rect x=\"29\" y=\"26\" width=\"6\" height=\"16\"/>\r\n        <rect x=\"21\" y=\"22\" width=\"6\" height=\"20\"/>\r\n        <rect x=\"13\" y=\"32\" width=\"6\" height=\"10\"/>\r\n        <rect x=\"5\" y=\"28\" width=\"6\" height=\"14\"/>\r\n    </g>\r\n    <g fill=\"#3F51B5\">\r\n        <circle cx=\"8\" cy=\"16\" r=\"3\"/>\r\n        <circle cx=\"16\" cy=\"18\" r=\"3\"/>\r\n        <circle cx=\"24\" cy=\"11\" r=\"3\"/>\r\n        <circle cx=\"32\" cy=\"13\" r=\"3\"/>\r\n        <circle cx=\"40\" cy=\"9\" r=\"3\"/>\r\n        <polygon points=\"39.1,7.2 31.8,10.9 23.5,8.8 15.5,15.8 8.5,14.1 7.5,17.9 16.5,20.2 24.5,13.2 32.2,15.1 40.9,10.8\"/>\r\n    </g>\r\n</svg>\r\n",
    "fc-sports_mode": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <circle fill=\"#FF9800\" cx=\"28\" cy=\"9\" r=\"5\"/>\r\n    <path fill=\"#00796B\" d=\"M29,27.3l-9.2-4.1c-1-0.5-1.5,1-2,2c-0.5,1-4.1,7.2-3.8,8.3c0.3,0.9,1.1,1.4,1.9,1.4c0.2,0,0.4,0,0.6-0.1 L28.8,31c0.8-0.2,1.4-1,1.4-1.8C30.2,28.4,29.7,27.6,29,27.3z\"/>\r\n    <path fill=\"#009688\" d=\"M26.8,15.2l-2.2-1c-1.3-0.6-2.9,0-3.5,1.3L9.2,41.1c-0.5,1,0,2.2,1,2.7c0.3,0.1,0.6,0.2,0.9,0.2 c0.8,0,1.5-0.4,1.8-1.1c0,0,9.6-13.3,10.4-14.9s4.9-9.3,4.9-9.3C28.7,17.4,28.2,15.8,26.8,15.2z\"/>\r\n    <path fill=\"#FF9800\" d=\"M40.5,15.7c-0.7-0.8-2-1-2.8-0.3l-5,4.2l-6.4-3.5c-1.1-0.6-2.6-0.4-3.3,0.9c-0.8,1.3-0.4,2.9,0.8,3.4 l8.3,3.4c0.3,0.1,0.6,0.2,0.9,0.2c0.5,0,0.9-0.2,1.3-0.5l6-5C41.1,17.8,41.2,16.6,40.5,15.7z\"/>\r\n    <path fill=\"#FF9800\" d=\"M11.7,23.1l3.4-5.1l4.6,0.6l1.5-3.1c0.4-0.9,1.2-1.4,2.1-1.5c-0.1,0-0.2,0-0.2,0h-9c-0.7,0-1.3,0.3-1.7,0.9 l-4,6c-0.6,0.9-0.4,2.2,0.6,2.8C9.2,23.9,9.6,24,10,24C10.6,24,11.3,23.7,11.7,23.1z\"/>\r\n</svg>\r\n",
    "fc-search": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <g fill=\"#616161\">\r\n        <rect x=\"34.6\" y=\"28.1\" transform=\"matrix(.707 -.707 .707 .707 -15.154 36.586)\" width=\"4\" height=\"17\"/>\r\n        <circle cx=\"20\" cy=\"20\" r=\"16\"/>\r\n    </g>\r\n    <rect x=\"36.2\" y=\"32.1\" transform=\"matrix(.707 -.707 .707 .707 -15.839 38.239)\" fill=\"#37474F\" width=\"4\" height=\"12.3\"/>\r\n    <circle fill=\"#64B5F6\" cx=\"20\" cy=\"20\" r=\"13\"/>\r\n    <path fill=\"#BBDEFB\" d=\"M26.9,14.2c-1.7-2-4.2-3.2-6.9-3.2s-5.2,1.2-6.9,3.2c-0.4,0.4-0.3,1.1,0.1,1.4c0.4,0.4,1.1,0.3,1.4-0.1 C16,13.9,17.9,13,20,13s4,0.9,5.4,2.5c0.2,0.2,0.5,0.4,0.8,0.4c0.2,0,0.5-0.1,0.6-0.2C27.2,15.3,27.2,14.6,26.9,14.2z\"/>\r\n</svg>\r\n",
    "bx-i-run": "<svg xmlns=\"http://www.w3.org/2000/svg\"  viewBox=\"0 0 24 24\"><circle cx=\"17\" cy=\"4\" r=\"2\"/><path d=\"M15.777 10.969a2.007 2.007 0 0 0 2.148.83l3.316-.829-.483-1.94-3.316.829-1.379-2.067a2.01 2.01 0 0 0-1.272-.854l-3.846-.77a1.998 1.998 0 0 0-2.181 1.067l-1.658 3.316 1.789.895 1.658-3.317 1.967.394L7.434 17H3v2h4.434c.698 0 1.355-.372 1.715-.971l1.918-3.196 5.169 1.034 1.816 5.449 1.896-.633-1.815-5.448a2.007 2.007 0 0 0-1.506-1.33l-3.039-.607 1.772-2.954.417.625z\"/></svg>",
    "fa-s-bed": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 640 512\"><!--! Font Awesome Free 6.3.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2023 Fonticons, Inc. --><path d=\"M32 32c17.7 0 32 14.3 32 32V320H288V160c0-17.7 14.3-32 32-32H544c53 0 96 43 96 96V448c0 17.7-14.3 32-32 32s-32-14.3-32-32V416H352 320 64v32c0 17.7-14.3 32-32 32s-32-14.3-32-32V64C0 46.3 14.3 32 32 32zm144 96a80 80 0 1 1 0 160 80 80 0 1 1 0-160z\"/></svg>",
    "cb-add": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M17 15V8h-2v7H8v2h7v7h2v-7h7v-2z\"/></svg>",
    "gm-walk": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path path fill=\"transparent\" d=\"M0 0h512v512H0z\"/><path fill=\"currenColor\" d=\"M271.9 25.85c-18.4 0-36 16.73-39.2 40.97-3.4 25.83 11.3 47.48 30.9 49.88 19.7 2.4 39.5-14.9 43-40.77 3.5-25.86-11.5-47.43-31-49.85-1.2-.15-2.5-.23-3.7-.23zm-38.2 95.75c-38 6.3-75.2 41.9-94 66-11.5 33.4-14.5 66.7-20.7 100l28.2-.8c6.6-25.2 8.2-51.9 21-75.4 14.8-8.2 26.9-20.6 38.4-33.8-6.4 32.9-14 72.3-13.2 101.8 29.8 70.9 95.7 140.4 133 206.4l29.9-24.3c-28.8-55.7-57.5-106.4-94.3-160.2 2.1-7.1 43.3-163.5 28-171.9-20.2 9-40.9 6-56.3-7.8zm71.9 58.6c-3.1 17.8-5.3 35.1-10 52.8 4.5 5.4 7.5 10.5 14.3 15.3 26 15.3 52 26.3 78 36.7l9.2-29.9-77.5-37.6c-4.3-12.5-7.9-25.2-14-37.3zM187.1 310.1c-5.1 25-9.8 50.2-11.6 76.5-15.9 31.7-35.7 51.6-60.5 76.4l30 23.2c23.1-25 49.9-47 66.7-73.8l14-42.6c-15.7-19-28.4-38.8-38.6-59.7z\"/></svg>",
    "gm-meal": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path path fill=\"transparent\" d=\"M0 0h512v512H0z\"/><path fill=\"currenColor\" d=\"M445.588 56l-.026 384.352c6.881 11.323 14 15.677 19.97 15.648 5.924-.028 12.967-4.434 19.714-15.418L466.82 244.27l-.215-2.391 1.475-1.906c21.174-27.169 28.573-74.108 22.533-113.81-3.02-19.852-9.342-37.82-18.195-50.522-7.424-10.652-16.28-17.447-26.828-19.641h-.002zm-372.375.004l-.016 67.127-12.56-.016V56.008H46.332l.002 67.11H33.756v-67.11h-14.57v103.228c-.001 11.417 6.23 17.748 16.04 21.662l4.06 1.622-.09 4.37c-2 84.57-3.977 169.139-5.962 253.708C40.074 451.79 47.1 456.028 52.95 456c5.85-.028 12.87-4.377 19.623-15.432-2.315-84.547-4.63-169.096-6.941-253.644l-.12-4.4 4.073-1.606c10.324-4.106 17.039-11.074 17.039-21.676V56.004h-13.41zM256 95A161 161 0 0 0 95 256a161 161 0 0 0 161 161 161 161 0 0 0 161-161A161 161 0 0 0 256 95z\"/></svg>",
    "gm-measure-tape": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path path fill=\"transparent\" d=\"M0 0h512v512H0z\"/><path fill=\"currenColor\" d=\"M442.2 35.42c-19.9 1.56-39 3.76-57.5 6.46l6.5 24.56-8.7 2.31-8.7 2.31-7-26.36c-20.1 3.4-39.4 7.46-58 12.03l8.8 25.71-17 5.8-9.2-26.94c-19.6 5.34-38.4 11.23-56.3 17.62l10.7 26.98-16.8 6.6-10.8-27.3c-18.3 7.01-35.9 14.49-52.6 22.2l12.5 27.3-16.4 7.6-12.4-27c-20.2 9.9-39.2 20.1-57.44 30.5l15.74 24.7-15.2 9.6-16.1-25.3c-15.41 9-30.36 18.1-44.64 27-4.13 9.7-2.57 20 2.4 29.4 4.45 8.4 11.77 15.1 18.18 18.5C165.9 146.1 325.8 96.65 480.5 70.12c-8.6-12.79-22.7-27.12-38.3-34.7zM241 185.1c-12.8 0-25.7.2-38.6.5l-.3 18.7-18-.2.3-18c-8.7.2-17.3.5-26 .8-28.4 14-55.2 29.3-79.82 46.1 132.82-22.7 276.22-6.6 388.62 21.3l-.1-.1c.2.1 1.1.2 3.8-2.9 2.8-3.2 5.8-8.8 8-14.8s3.6-12.5 3.8-17.2c.1-2.3-.3-3.9-.5-4.8-10.3-3.3-20.9-6.2-31.8-8.8-17.1-4.2-34.8-7.6-53-10.4l-6 19.4-8.6-2.7-8.6-2.7 5.1-16.4c-13.6-1.7-27.4-3.2-41.5-4.3l-2.5 19-17.8-2.4 2.3-17.8c-16.3-1-32.9-1.7-49.7-2l.3 16.6-18 .4-.3-17.2c-3.7 0-7.4-.1-11.1-.1zm92.4 62.9c-13.4 3.4-26.4 7.2-39 11.4l10.2 22.4-16.4 7.4-10.8-23.8c-17.9 6.6-34.8 14-50.5 22.1l11.5 18.9-15.4 9.4-11.9-19.7c-8.6 4.9-16.8 10-24.5 15.3-8.8 6.1-17 12.5-24.6 19.1l19.4 15.3-11.2 14.2-21.5-17c-14 14-25.1 28.8-32.9 44.1-1.4 2.8-2.7 5.7-3.9 8.6 8.3-1.3 16.1-1.8 23.6-1.7 15 .3 28.4 3.3 40.7 7.9.1-.3.1-.7.2-1 5.7-20.8 20.2-38.8 43-55.1 40.3-28.7 108.3-53.4 209.5-81.8-21.4-4.6-43.7-8.7-66.7-12.1l2.6 6.6-16.8 6.6-6.3-16c-2.8-.4-5.5-.8-8.3-1.1zM135.3 411.5c-9.1-.1-18.9 1-29.8 3.6-4.4 18.2-4.2 36.9 1.2 56 .5.5 4.8 3.5 13.2 4.7 8.8 1.2 20.3.9 31.3-.5 10.9-1.4 21.4-4 27.7-6.7 1.8-.8 3.1-1.5 4.1-2.2-6.3-16.4-9.5-31.8-9.2-46.4-11.6-5.1-24.1-8.3-38.5-8.5z\"/></svg>",
    "gm-water-bottle": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\"><path path fill=\"transparent\" d=\"M0 0h512v512H0z\"/><path fill=\"currenColor\" d=\"M121.4 33v30h78.1V33zm11 48c-16.4 16.58-42.95 46.2-42.95 71v39H231.5v-39c0-24.8-26.5-54.42-43-71zM89.45 209v16H231.5v-16zm0 34v16H231.5v-16zm0 34v16H231.5v-16zm0 34v170.2c43.05 12.7 98.95 12.7 142.05 0V311zm224.85 34l14.3 142H345l-6.9-118.5 18-1L363 487h45.3l14.3-142z\"/></svg>"
}
FX.setIcons(usedIcons);
