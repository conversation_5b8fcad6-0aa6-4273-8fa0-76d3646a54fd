import { FxElement, html, css } from '/fx.js';
import { $styles } from './ftmp.x.js';
import '../button/button.js';
import '../checkbox/checkbox.js';
import '../property-grid/property-grid.js';
import '../table/table.js';
import '../tree/tree.js';
import '../splitter/splitter.js';
import { BS_ITEM } from '../base/src/base-item.js';

export class FxFtmp extends FxElement {
    static properties = {
        base: { type: Object },
        item: { type: Object, notify: true },
        selectedField: { type: Object },
        label: { type: String },
        isReady: { type: Boolean, default: false },
        highlightSelected: { type: Boolean, default: false, save: true },
        showConstructor: { type: Boolean, default: false }
    }

    get flat() { return FX.flatItems(this.item?.fields || {}, true) }
    get all() { return FX.allItems(this.item?.fields || {}) }
    get root() { return this.item?.fields || {} }
    get label() { return this.root?.$$$?.label0 || '' }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this.init();
            this.isReady = true;
            this.style.opacity = 1;
            this.$update();
        }, 100)
    }
    'item-changed'(e) {
        if (!this.isReady) return;
        this.init();
    }
    async init() {
        const db = this.base?.dbLocal;
        if (!db || !this.base.fxSelected?.if?.ifForm) return;
        if (this.base.bsFlatDrafts['form-item-' + this.base.fxSelected._id]) {
            this.item = this.base.bsFlatDrafts['form-item-' + this.base.fxSelected._id];
            this.selectedField = this.item?.fields;
            this.$update();
            return;
        }
        let constructorId, useParentConstructor, parents = [];
        if (!this.base.fxSelected.if?.ifConstructor) {
            let parent = this.base.fxFlat[this.base.fxSelected.parentId];
            while (parent) {
                if (parent?.if.ifConstructor) {
                    constructorId = parent._id;
                    useParentConstructor = true;
                    break;
                }
                parents.unshift(parent);
                parent = this.base.fxFlat[parent.parentId];
            }
        } else {
            constructorId ||= this.base.fxSelected._id;
        }
        if (!constructorId) {
            return;
        }
        const constructorKey = 'field:' + constructorId;
        const selectedKey = 'field:' + this.base.fxSelected._id;
        const constructorAllDocs = await db.allDocs({ include_docs: true, startkey: constructorKey, endkey: constructorKey + '\ufff0' });
        const flat = {}, parentsFlat = {};
        let root;
        if (useParentConstructor) {
            let parentAllDocs;
            await Promise.all(parents.map(async i => {
                const parentKey = 'field:' + i._id;
                parentAllDocs = await db.allDocs({ include_docs: true, startkey: parentKey, endkey: parentKey + '\ufff0' });
                parentAllDocs.rows.map(p => {
                    const doc = p.doc;
                    const $$$ = doc.$$$ || {};
                    delete doc.$$$;
                    delete doc._rev;
                    delete doc.value;
                    const _id = doc._id.split(':').at(-1);
                    parentsFlat[_id] = { ...doc };
                    parentsFlat[_id].$$$ = {};
                    Object.keys($$$).forEach(k => {
                        if ($$$[k] || $$$[k] === 0 || $$$[k] === null) {
                            parentsFlat[_id].$$$[k] = $$$[k];
                            parentsFlat[_id].color ||= 'blue';
                        }
                    })
                })
            }))
            let selectedAllDocs = await db.allDocs({ include_docs: true, startkey: selectedKey, endkey: selectedKey + '\ufff0' });
            selectedAllDocs.rows.map(p => {
                const doc = p.doc;
                const $$$ = doc.$$$ || {};
                delete doc.$$$;
                delete doc._rev;
                const _id = doc._id.split(':').at(-1);
                parentsFlat[_id] = { ...doc };
                parentsFlat[_id].$$$ = {};
                Object.keys($$$).forEach(k => {
                    if ($$$[k] || $$$[k] === 0 || $$$[k] === null) {
                        parentsFlat[_id].$$$[k] = $$$[k];
                        parentsFlat[_id].color ||= 'blue';
                    }
                })
            })
        }
        constructorAllDocs?.rows.map(i => {
            const doc = i.doc;
            let _id = doc._id;
            delete doc._rev;
            if (useParentConstructor) {
                _id = doc._id = selectedKey + ':' + _id.split(':').at(-1);
                if (doc.parentId) {
                    doc.parentId = selectedKey + ':' + doc.parentId.split(':').at(-1);
                }
            }
            const props = { id: _id.split(':').at(-1), parent_id: doc.parentId?.split(':').at(-1) };
            const item = new BS_ITEM({ _id, props, type: 'field', isLoad: true, doc });
            if (_id.endsWith(':$root-field'))
                root = item;
            else
                flat[_id] = item;
        })
        Object.values(parentsFlat).map(i => {
            let doc = i;
            const _id = doc._id;
            const $$$ = doc.$$$;
            delete doc.$$$;
            const selectedId = selectedKey + ':' + _id.split(':').at(-1);
            if (doc.parentId)
                doc.parentId = selectedKey + ':' + doc.parentId.split(':').at(-1);
            if (flat[selectedId]?.doc) {
                doc = { ...flat[selectedId].doc, ...doc }
                doc.$$$ = {};
                Object.keys($$$).forEach(k => {
                    if ($$$[k] || $$$[k] === 0 || $$$[k] === null)
                        doc.$$$[k] = $$$[k]
                })
                doc.sort = flat[selectedId].sort;
            } else {
                doc.color = 'red';
            }
            const props = { id: _id.split(':').at(-1), parent_id: doc.parentId?.split(':').at(-1), color: doc.color };
            const item = new BS_ITEM({ _id: selectedId, props, type: 'field', isLoad: true, doc });
            flat[selectedId] = item;
        })
        this.item.fields = await this.base.buildTree(flat, selectedKey + ':$root-field', 'field', root);
        this.item.fields.icon ||= 'flat-color-icons:services:32';
        this.item.fields.label ||= 'fields';
        this.selectedField = this.item?.fields;
        this.base.bsFlatDrafts ||= {};
        this.base.bsFlatDrafts['form-item-' + this.base.fxSelected._id] = this.item;
        this.$update();
    }
    on_fieldSelected(e) {
        this.async(async () => {
            let item;
            if (e)
                item = e?.detail?.item || this.fxFlat?.[e?.detail?.row?._id];
            item ||= this.root;
            this.selectedField = item;
            this.$update();
            if (item._id.endsWith('$root-field') && this.isShowFieldsSets) {
                FX.closeDD();
                this.isShowFieldsSets = false;
                this.showFormSets();
            } else if (!item._id.endsWith('$root-field') && this.isShowFormSets) {
                FX.closeDD();
                this.isShowFormSets = false;
                this.showFieldSets();
            } else if (this.isShowFieldsSets) {
                this.$update();
                const grid = document.querySelectorAll('fx-grid')[0];
                grid.item = this.dataFieldSets();
                grid.parentElement.label = 'Field settings - ' + this.base.fxSelected.label;
                grid?.$update();
            } else if (this.isShowFormSets) {
                this.$update();
                const grid = document.querySelectorAll('fx-grid')[0];
                grid.item = this.dataFormSets();
                grid.parentElement.label = 'Form settings - ' + this.base.fxSelected.label;
                grid?.$update();
            }
        }, 100)
        this.$update();
    }
    async on_fieldAdd(e) {
        if (!this.selectedField?._id) return;
        const key = 'field:' + this.base.fxSelected._id,
            _id = key + ':' + FX.ulid();
        const props = { id: _id.split(':').at(-1), parent_id: this.base.fxSelected.parentId?.split(':').at(-1) };
        const newItem = new BS_ITEM({ _id, label: 'new field', props, isNew: true, type: 'field', parentId: this.selectedField._id });
        this.selectedField.items ||= [];
        this.selectedField.items.push(newItem);
        this.selectedField.expanded = true;
        newItem.sort = this.selectedField.items.length;
        this.$update();
    }
    showSets() {
        if (this.selectedField._id?.endsWith('$root-field')) {
            this.showFormSets();
        } else {
            this.showFieldSets(this.selectedField);
        }
    }

    static styles = $styles.ftmp

    render() {
        const root = this.root.$$$;
        const data = this.root.value || [];
        return html`
            <div class="form horizontal flex w100 h100 relative box" @click=${this.formClick}>
                ${this.showConstructor ? html`  
                    <fx-tree id="fx-ftmp-tree" .item=${this.item?.fields} style="min-width: 164px;  width: 280px;" @selected=${this.on_fieldSelected} @add-item=${this.on_fieldAdd} remoteAddItem>
                        <div class="horizontal flex" slot="panel">
                            <div class="flex"></div>
                            <fx-icon id="btn-tree-sets" class="but ml8" url="flat-color-icons:services" scale=.8 an="btn" br="square" @click=${this.showSets}></fx-icon>
                        </div> 
                    </fx-tree>
                    <fx-splitter color="gray" vertical size="1"></fx-splitter>
                ` : html``}
                 ${!this.showConstructor && this.base?.fxSelected?.if?.ifResult === 'table' ? html`
                    <fx-table id=${root?.tableId} .data=${data} class="w100" .base=${this.base} .templateId=${root?.tableTemplateId} .hide=${root?.tableHide || 'cb'}></fx-table>
                 ` : html` 
                    <div class="form w100 h100 relative${this.root.$$$?.class || ''}" style=${this.root.$$$?.style} @click=${this.formClick}>
                        <div class="w100 brb horizontal mh36 p8 center no-flex box ${this.label ? '' : 'hidden'} ${this.root.$$$?.classRoot || ''}" style=${this.root.$$$?.style0 || ''}>${this.label || ''}</div>
                        <div class="vertical flex h100 w100 relative overflow-h">
                            <div class="w100 vertical overflow-y flex relative">
                                <fx-ftmp-field class="mr4 box flex" .main=${this} .field=${this.root}></fx-ftmp-field>
                            </div>
                        </div>
                    </div>
                `}
            </div>
        `
    }

    async showFormSets() {
        let templateList = await this.getTemplatesList();
        templateList.unshift('');
        const run = async (e, item) => {
            let id = item.id;
            if (id === 'templateEditorJS') {
                const res = await FX.show('form', 'table-template', {
                    type: 'ace',
                    mode: 'javascript',
                    showBtnSettings: true,
                    slot: 'main',
                    templateId: this.root.$$$?.tableTemplateId 
                }, {
                    id: 'table-template-editor',
                    label: 'Table Template Editor - JavaScript',
                    ok: 'Apply',
                    cancel: 'Cancel',
                    showHeader: true,
                    fixed: true,
                })
            }
            else if (id === 'templateEditor') {
                const res = await FX.show('form', 'ftmp-template', {
                    showBtnSettings: true,
                    slot: 'main',
                    templateId: this.root.$$$?.templateTemplateId,
                    base: this.base
                }, {
                    id: 'template-editor',
                    label: 'Template Editor',
                    ok: 'Apply',
                    cancel: 'Cancel',
                    showHeader: true,
                    fixed: true,
                })
            }
            this.$update();
            this.$qs('fx-tree')?.$update();
            document.querySelectorAll('fx-grid')?.[0]?.$update();
        }
        const itemsResult = () => {
            if (this.base.fxSelected.if.ifResult === 'table') {
                return [
                    { icon: 'ant-design:number-outlined:28', label: 'table id', subLabel: 'id таблицы', get: () => this.root.$$$?.tableId || '', set: (e) => this.root.$$$.tableId = e, run },
                    { icon: 'carbon:table-split:28', label: 'template id', subLabel: 'шаблон таблицы', get: () => this.root.$$$?.tableTemplateId || '', set: (e) => this.root.$$$.tableTemplateId = e, is: 'select', options: templateList, run },
                    { icon: 'carbon:table-split:28', label: 'hide cbtfnsae', subLabel: 'скрыть / показать', get: () => this.root.$$$?.tableHide || 'cb', set: (e) => this.root.$$$.tableHide = e, run },
                    { id: 'templateEditorJS', icon: 'material-symbols-light:code', label: 'template editor', subLabel: 'редактор шаблона таблицы', value: 'edit', is: 'button', run },
                ]
            } if (this.base.fxSelected.if.ifResult === 'template') {
                return [
                    { icon: 'ant-design:number-outlined:28', label: 'template id', subLabel: 'id шаблона', get: () => this.root.$$$?.templateId || '', set: (e) => this.root.$$$.templateId = e, run },
                    { id: 'templateEditor', icon: 'material-symbols-light:code', label: 'template editor', subLabel: 'редактор шаблонов', value: 'edit', is: 'button', run },
                ]
            }
        }
        const item = this.dataFormSets = () => [
            { id: 'highlightSelected', fill: 'var(--fx-color-selected)', icon: 'ant-design:alert-outlined:28', label: 'highlight selected', subLabel: 'подсвечивать выбранные', get: () => this.highlightSelected, is: 'checkbox', set: (e) => this.highlightSelected = e, run },
            { id: 'result', icon: 'flat-color-icons:parallel-tasks:28', label: 'type result', subLabel: 'результирующий тип', get: () => this.base.fxSelected.if.ifResult, is: 'select', set: (e) => { this.base.fxSelected.if ||= {}; this.base.fxSelected.if.ifResult = e}, run, items: itemsResult(), options: ['', 'table', 'template' ] },
            {
                id: 'forms-set', icon: 'flat-color-icons:services:28', label: 'form sets', subLabel: 'установки формы', value: this.root._id, is: 'span', expanded: true, items: [
                    { id: 'class', icon: '', label: 'form class', subLabel: 'класс формы', get: () => this.root.$$$?.class || '', set: (e) => this.root.$$$.class = e, run, is: 'textarea' },
                    { id: 'style', icon: '', label: 'form style', subLabel: 'стиль формы', get: () => this.root.$$$?.style || '', set: (e) => this.root.$$$.style = e, run, is: 'textarea' },
                ]
            },
            {
                id: 'label', icon: '', label: 'form label', subLabel: 'метка формы', get: () => this.root.$$$?.label0 || '', set: (e) => this.root.$$$.label0 = e, run, expanded: true, items: [
                    { id: 'label-class', icon: '', label: 'label class', subLabel: 'класс метки', get: () => this.root.$$$?.classRoot || '', set: (e) => this.root.$$$.classRoot = e, run, is: 'textarea' },
                    { id: 'label-style', icon: '', label: 'label style', subLabel: 'стиль метки', get: () => this.root.$$$?.style0 || '', set: (e) => this.root.$$$.style0 = e, run, is: 'textarea' },
                ]
            },
        ]
        this.isShowFormSets = true;
        await FX.showGrid({ type: 'form-sets', id: this._id + '-form-sets', item: item(), rowHeight: 32, hideSelected: true }, { id: this._id + '-fields-sets', label: 'Form settings - ' + this.base.fxSelected.label, intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowFormSets = false;
    }
    async getTemplatesList() {
        if (!this.base?.dbLocal) return [];
        try {
            const result = await this.base.dbLocal.allDocs({
                include_docs: true,
                startkey: 'table-template:',
                endkey: 'table-template:\ufff0'
            })
            return result.rows.map(row => row.id.replace('table-template:', ''));
        } catch (error) {
            console.error('Error getting templates list:', error);
            return [];
        }
    }
    async showFieldSets() {
        let templateList = await this.getTemplatesList();
        templateList.unshift('');
        const run = async (e, item) => {
            let id = item._id || item.id;
            if (id === 'selectIcon') {
                let res = await FX.show('dropdown', '/icons/icons.js', { isSelectIcon: true }, {});
                if (res?.detail?.res) {
                    let spl = res.detail.res.split(':'),
                        icon = spl[0] + (spl[1] ? ':' + spl[1] : '');
                    this.selectedField.$$$.fIcon = icon;
                }
            }
            else if (id === 'is') {
                const v = item.value;
                const sets = {
                    'row': 'lucide:rectangle-horizontal',
                    'table': 'carbon:table-split',
                    'tab': 'fluent-mdl2:folder-horizontal',
                    'tabs': 'carbon:collapse-all',
                    'label': 'carbon:label',
                    'icon': 'carbon:image',
                    'dummy': 'bi:border',
                    'input': 'vaadin:input',
                    'c-label': 'ph:text-indent',
                    'splitter': 'f7:resize-v',
                }
                this.selectedField.icon = sets[v] || '';
                if (v && (this.selectedField.label === 'new field' || this.selectedField.label === '...'))
                    this.selectedField.label = v;
            }
            else if (id === 'deleteIcon') {
                delete this.selectedField.$$$?.fIcon;
            }
            this.$update();
            this.$qs('fx-tree').$update();
            document.querySelectorAll('fx-grid')[0].$update();
        }
        const itemsIs = () => {
            if (this.selectedField.is === 'table') {
                return [
                    { icon: 'ant-design:number-outlined:28', label: 'table id', subLabel: 'id таблицы', get: () => this.selectedField.$$$?.tableId || '', set: (e) => this.selectedField.$$$.tableId = e, run },
                    { icon: 'carbon:table-split:28', label: 'template id', subLabel: 'шаблон таблицы', get: () => this.selectedField.$$$?.tableTemplateId || '', set: (e) => this.selectedField.$$$.tableTemplateId = e, is: 'select', options: templateList, run },
                    { icon: 'carbon:table-split:28', label: 'hide cbtfnsae', subLabel: 'скрыть / показать', get: () => this.selectedField.$$$?.tableHide || 'cb', set: (e) => this.selectedField.$$$.tableHide = e, run },
                ]
            } else if (this.selectedField.is === 'input') {
                return [
                    { id: 'isType', icon: '', label: 'type', subLabel: 'тип поля', get: () => this.selectedField.isType || '', set: (e) => this.selectedField.isType = e, is: 'select', options: ['', 'text', 'checkbox', 'button', 'color', 'date', 'datetime-local', 'email', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'time', 'url', 'week'], run },
                ]
            }
        }
        const item = this.dataFieldSets = () => [
            {
                id: 'id', icon: 'flat-color-icons:tree-structure:28', label: 'field id', subLabel: 'id поля', get: () => this.selectedField._id || '', set: (e) => this.selectedField._id = e.value, run, is: 'span', expanded: true, items: [
                    { id: 'name', icon: '', label: 'name', subLabel: 'имя поля', get: () => this.selectedField.label || '', set: (e) => this.selectedField.label = e, run },
                    { id: 'is', icon: '', label: 'field is', subLabel: 'is поля', get: () => this.selectedField.is || '', set: (e) => this.selectedField.is = e, is: 'select', options: ['', 'row', 'label', 'c-label', 'tabs', 'tab', 'table', 'icon', 'input', 'dummy', 'splitter'], run, items: itemsIs() },
                    { id: 'class', icon: '', label: 'class', subLabel: 'класс поля', get: () => this.selectedField.$$$?.class || '', set: (e) => this.selectedField.$$$.class = e, is: 'textarea', run },
                    { id: 'style', icon: '', label: 'style', subLabel: 'стиль поля', get: () => this.selectedField.$$$?.style || '', set: (e) => this.selectedField.$$$.style = e, is: 'textarea', run },
                    { id: 'style', icon: '', label: 'container style', subLabel: 'стиль контейнера', get: () => this.selectedField.$$$?.fstyle || '', set: (e) => { this.selectedField.data ||= {}; this.selectedField.$$$.fstyle = e }, is: 'textarea', run },
                ]
            },
            {
                id: 'iconName', icon: 'flat-color-icons:gallery:28', label: 'icon name', subLabel: 'иконка', get: () => this.selectedField.$$$?.fIcon, set: (e) => this.selectedField.$$$.fIcon = e, run, items: [
                    { id: 'selectIcon', icon: 'flat-color-icons:gallery:28', label: 'set icon', value: 'select icon', is: 'button', run },
                    { id: 'sizeIcon', icon: 'flat-color-icons:edit-image:28', label: 'icon size', subLabel: 'размер иконки', get: () => this.selectedField.$$$?.fIconSize || 24, set: (e) => this.selectedField.$$$.fIconSize = e, type: 'number', run },
                    { id: 'deleteIcon', icon: 'fx:close:28', fill: 'red', label: 'delete icon', value: 'delete icon', is: 'button', run },

                ]
            },
            { icon: 'tabler:eye-closed:28', label: 'hidden field', subLabel: 'скрыть / показать', get: () => this.selectedField.hidden, set: (e) => { this.selectedField.hidden = e; this.$update() }, is: 'checkbox', run },

        ]
        this.isShowFieldsSets = true;
        await FX.showGrid({ type: 'fields-sets', id: this._id + '-fields-sets', item: item(), rowHeight: 32, hideSelected: true }, { id: this._id + '-fields-sets', label: 'Fields settings - ' + this.base.fxSelected.label, intersect: true, align: 'left', class: 'br', minWidth: 300, draggable: true, resizable: false, btnCloseOnly: true }, true);
        this.isShowFieldsSets = false;
    }
}
customElements.define('fx-ftmp', FxFtmp);

customElements.define('fx-ftmp-field', class FxFtmpField extends FxElement {
    static properties = {
        main: { type: Object },
        item: { type: Object },
        root: { type: Object },
        selectedField: { type: Object },
        field: { type: Object },
        flex: { type: String, default: 'unset' }
    }
    get base() { return this.main?.base || {} }
    get item() { return this.main?.item || {} }
    get selectedField() { return this.main?.selectedField || {} }
    get root() { return this.main?.item?.fields || {} }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this.style.setProperty('--flex', this.flex || 'unset');
            this.isReady = true;
            this.$update();
        }, 100)
    }
    isSelected(i) {
        if (this.main.highlightSelected)
            return this.selectedField?._id === i._id ? 'selected2' : ''
        return '';
    }
    templateChanged(e, item) {
        item.$$$ ||= {}
        item.$$$.tableTemplateId = e.detail.templateId;
        this.$update();
    }

    static styles = css`
        :host {
            display: flex;
            flex-direction: column;
            justify-content: start;
            /* flex: var(--flex, unset); */
            position: relative;
            flex-wrap: wrap;
            box-sizing: border-box;
            margin: 4px;
            margin-right: 0px;
        }
        .selected2 {
            /* box-shadow: 0 4px 2px -2px var(--fx-color-selected); */
            outline: 2px solid var(--fx-color-selected);
        }
    `

    _isRow(i) {
        if (i.hidden) return html``;
        return html`
            <div class="horizontal w100 wrap ${this.isSelected(i)} ${i.$$$?.class || ''}" style="${this.root?.style || ''}; ${i?.$$$?.style || ''}">
                ${i?.items?.map(f => {
            return html`<fx-ftmp-field .main=${this.main} class="${f.$$$?.class || ''} ${this.isSelected(f)}" .field=${{ items: [f] }} flex='1'></fx-ftmp-field>`
        })}
            </div>
        `
    }
    _isTabs(i) {
        if (i.hidden) return html``;
        return html`
            <div class="vertical flex h100 relative w100 ${i?.$$$?.class || ''}" style="${this.root.$$$?.style || ''}; ${i.$$$?.style || ''}">
                <div class="horizontal w100 brb mt4 ${this.isSelected(i)}">
                    ${(i.items || []).filter(d => !d._deleted && !d.hidden).map((t, tidx) => {
            return html`
                            <div class="tabs m2 p4 br pointer ${this.isSelected(t)} ${tidx === (this.tidx || 0) ? 'selected' : ''}" style=" border-bottom: 0; margin-bottom: 0; border-radius: 4px 4px 0 0;" @click=${e => { this.tidx = tidx; this.$update(); }}>
                                ${t.label}
                            </div>
                        `
        })}
                </div>
                <div class="w100 vertical overflow-y flex relative" style="min-height: 0">
                    <fx-ftmp-field .main=${this.main} class="m4 flex" .field=${i.items?.[this.tidx || 0]} flex="1"></fx-ftmp-field>
                </div>
            </div>
        `
    }
    _isLabel(i) {
        if (i.hidden) return html``;
        return html`
            <div class="w100 ${i.items?.length ? 'min-w100' : ''} ${i.$$$?.class || ''} ${this.isSelected(i)}" style="${this.root.$$$?.style || ''} ${i.$$$?.style || ''}">${i.label}</div>
        `
    }
    _isIcon(i) {
        if (i.hidden) return html``;
        return html`
            <fx-icon url=${i.iconName || 'fx:menu'} size=${i.iconSize || 24} class="${i.$$$?.class || ''} ${this.isSelected(i)}" style="${this.root.$$$?.style || ''} ${i.$$$?.style || ''}" an="btn" @click=${e => this._fieldClick(i)} fill=${i.iconColor || ''} svg=${i.iconSVG || ''} scale=${i.iconScale || ''} title=${i.iconTitle || ''}></fx-icon>
        `
    }
    _isTable(i) {
        if (i.hidden) return html``;
        const data = i.value || [];
        return html`
            <div class="horizontal relative w100 overflow-h overflow-x bral box flex ${this.isSelected(i)}  ${i.$$$?.class || ''}" style="min-height: 150px; ${i.$$$?.style || ''}">  
                <fx-table id=${i.$$$?.tableId} .data=${data} class="w100 ${i.$$$?.class || ''} ${this.isSelected(i)}" .base=${this.base} @table-changed=${e => this._tableChanged(e, i)} @template-changed=${e => this.templateChanged(e, i)} .templateId=${i.$$$?.tableTemplateId} .hide=${i.$$$?.tableHide || 'cb'} style="${this.root.$$$?.style || ''} ${i.$$$?.style || ''}"></fx-table>
            </div>
        `
    }
    _isInput(i) {
        if (i.hidden) return html``;
        return html`
            <fx-ftmp-input .main=${this.main} class="${this.root.$$$?.class || ''} ${i.$$$?.class || ''} ${this.isSelected(i)}" .field=${i} style="${this.root.$$$?.style || ''} ${i.$$$?.style || ''}">
            </fx-ftmp-input>
    `
    }
    _isDummy(i) {
        if (i.hidden) return html``;
        return html`
            <div class="${i.$$$?.class || ''} ${this.isSelected(i)}" style="${this.root.$$$?.style || ''} ${i.$$$?.style || ''}"></div>
        `
    }
    _isSplitter(i) {
        if (i.hidden) return html``;
        return html`
            <fx-splitter bottom size="0" class="w100 ${i.$$$?.class || ''} ${this.isSelected(i)}" style="${this.root.$$$?.style || ''} ${i.$$$?.style || ''}"></fx-splitter>
        `
    }

    render() {
        return html`
            ${this.field?.items?.map(i => {
            return i._deleted ? html`` : html`
                    ${i.is === 'input' || i.is === 'c-label' ? this._isInput(i)
                    : i.is === 'row' ? this._isRow(i)
                        : i.is === 'tabs' ? this._isTabs(i)
                            : i.is === 'label' ? this._isLabel(i)
                                : i.is === 'icon' ? this._isIcon(i)
                                    : i.is === 'table' ? this._isTable(i)
                                        : i.is === 'dummy' ? this._isDummy(i)
                                            : i.is === 'splitter' ? this._isSplitter(i)
                                                : this._isInput(i)
                }`
        })}
        `
    }

    _tableChanged(e, i) {
        i.value = e.detail.data;
        this.$update();
        this.base?.$update();
    }
})

customElements.define('fx-ftmp-input', class FxFtmpInput extends FxElement {
    static properties = {
        main: { type: Object },
        item: { type: Object },
        root: { type: Object },
        field: { type: Object },
        selectedField: { type: Object }
    }
    get item() { return this.main?.item || {} }
    get selectedField() { return this.main?.selectedField || {} }
    get root() { return this.item?.fields?.[0] || [] }
    get hasChildren() { return this.field?.items?.length }

    _toggleExpand(e) {
        this.field.expanded = e.target.toggled;
        this.$update();
    }

    _change(e) {
        this.field.value = e.target.value;
        this.$update();
    }

    static styles = css`
        :host {
            width: 100%;
            min-width: 0;
            display: flex;
            flex-direction: column;
            /* box-shadow: -1px -1px 0 0 lightgray; */
            position: relative;
            /* flex: 1; */
            /* min-width: 240px; */
        }
        .row {
            min-height: 34px;
        }
        .show {
            /* visibility: visible; */
            display: block;
        }
        .hide {
            /* visibility: hidden; */
            display: none;
        }
        .fieldset {
            border-radius: 4px;
        }
    `

    render() {
        return html`
            <div style="${this.field.$$$?.fstyle}">
                ${this.field?.is === 'c-label' ? html`
                    <div class="box row horizontal align w100 h100 m0 p0 relative" style="max-width: calc(100% - 16px);">
                        <fx-button class="${this.hasChildren ? 'show' : 'hide'}" name="cb-chevron-right" toggledClass="right90" .toggled="${this.field.expanded}" size="18" back="transparent" border=0 @click=${this._toggleExpand}></fx-button>
                        <fx-icon class="${this.hasChildren ? 'mr8 ml0' : 'ml4'} ${this.field?.$$$?.fIcon ? 'show' : 'hide'}" icon=${this.field?.$$$?.fIcon} scale=${this.field?.$$$?.fIconScale || 1} fill=${this.field?.$$$?.fIconFill || ''} size=${this.field?.$$$?.fIconSize || 24}></fx-icon>
                        <div class="${this.hasChildren ? 'hide' : 'show'}" style="width: 8px;"></div>
                        <label type=${this.field?.isType || 'text'} class="w100 inp fm ${this.field?.$$$?.class || ''}" style="height: 20px; ${this.root.$$$?.style || ''} ${this.field?.$$$?.style || ''}">
                            ${this.field?.label}
                        </label>
                        <div style="width: 8px;"></div>
                    </div>
                ` : html`
                    <fieldset class="box fieldset bral p0 m4 mr0 relative overflow-h ${this.root.fieldsetsClass || ''} ${this.field.fieldsetClass || ''}" style="${this.root.fieldsetsStyle || ''} ${this.field.fieldsetStyle || ''}">
                        <legend style="padding: 0; margin-left: 2px; font-size: smaller;">${this.field?.label}</legend>
                        <div class="row horizontal align w100 h100 m0 pt4 pb2 relative" style="margin-top: -4px;">
                            <fx-button class="${this.hasChildren ? 'show' : 'hide'}" name="cb-chevron-right" toggledClass="right90" .toggled="${this.field.expanded}" size="18" back="transparent" border=0 @click=${this._toggleExpand}></fx-button>
                            <fx-icon class="${this.hasChildren ? 'mr8 ml0' : 'ml4'} ${this.field?.$$$?.fIcon ? 'show' : 'hide'}" icon=${this.field?.$$$?.fIcon} scale=${this.field?.$$$?.fIconScale || 1} fill=${this.field?.$$$?.fIconFill || ''} size=${this.field?.$$$?.fIconSize || 24}></fx-icon>
                            <div class="${this.hasChildren ? 'hide' : 'show'}" style="width: 8px;"></div>
                            <input type=${this.field?.isType || 'text'} class="w100 inp fm flex ${this.field?.$$$?.class || ''}" style="height: 20px; ${this.root.$$$?.style || ''} ${this.field?.$$$?.style || ''}" .value=${this.field.value || ''} @change=${this._change} @input=${this._change}>
                            <div style="width: 8px;"></div>
                        </div>
                    </fieldset>
                `}
                ${this.hasChildren && this.field.expanded ? html`
                    <fx-ftmp-field style="margin-left: 18px;" .main=${this.main} .field=${this.field}></fx-ftmp-field>
                ` : html``}
            </div>
        `
    }
})
