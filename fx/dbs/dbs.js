import { FxElement, html, css } from '/fx.js';

import { $styles } from './dbs.x.js';
import '../button/button.js';

customElements.define('fx-dbs', class FxDbs extends FxElement {
    static properties = {
        base: { type: Object },
        defaultDB: { type: String, default: 'http://192.168.0.2:5984', save: true },
        defaultLocalDB: { type: String, default: 'http://192.168.0.2:5984', save: true },
        dbUrl: { type: String, default: '', save: true },
        dbLocalUrl: { type: String, default: '', save: true },
        dbPrefix: { type: String, default: 'aka-', save: true },
        dbLocalPrefix: { type: String, default: 'fox-', save: true },
        dataList: { type: Array, default: ['', 'aka-', 'bs-', 'fox-'] },
        dataLocalList: { type: Array, default: ['', 'aka-', 'bs-', 'fox-'] },
        dbList: { type: Array, default: [] },
        localDbList: { type: Array, default: [] },
        combinedDbList: { type: Array, default: [] },
        dbSelectedIdx: { type: Number, default: 0, save: true },
        dbLocal: { type: Object },
        dbRemote: { type: Object }
    }
    get dbSelected() { return this.combinedDbList[this.dbSelectedIdx] }

    constructor() {
        super();
        const separator = window.location.hash || window.location.search;
        this.hashParams = new URLSearchParams(separator.substring(1));
        const prefix = this.hashParams.get('pr')
        this.dbPrefix = localStorage.getItem('fx-base.dbPrefix') || 'aka-';
        this.dbLocalPrefix = localStorage.getItem('fx-base.dbLocalPrefix') || 'fox-';
        if (prefix)
            this.dbPrefix = prefix;
        this.dbPrefix ||= 'aka-';
        localStorage.setItem('fx-base.dbPrefix', this.dbPrefix);
        this.id = this.dbPrefix.replaceAll('$', '') + (this.id || 'fx-base-dbs');
        // console.log(this.id);
    }
    firstUpdated() {
        super.firstUpdated();
        this.async(async () => {
            if (this.dbUrl) {
                this.dbList = await FX.dbs(this.dbUrl, this.dbPrefix);
                const res = await Promise.all(this.dbList.map(async i => {
                    try {
                        const dbCredentials = FX.jdurix(this.dbUrl);
                        if (!dbCredentials || !dbCredentials.url || !dbCredentials.name || !dbCredentials.psw) {
                            console.warn('Database credentials not properly configured. Please configure them in settings.');
                            i.doc_count = 0;
                            i.size = '0.000';
                            return;
                        }
                        let db = await fetch(`${dbCredentials.url}/${i.name}`, {
                            headers: { 'Authorization': 'Basic ' + btoa(`${dbCredentials.name}:${dbCredentials.psw}`) }
                        });
                        db = await db.json();
                        i.doc_count = db.doc_count;
                        i.size = (db.sizes.active / 1024 / 1024).toFixed(3);
                    } catch (error) {
                        console.error('Error fetching database info:', error);
                        i.doc_count = 0;
                        i.size = '0.000';
                    }
                }))
            }
            this.loadLocalDbs();
            if (this.dbSelectedIdx >= this.combinedDbList.length) this.dbSelectedIdx = 0;
            this.getDB();
            this.async(() => {
                this.scrollToSelectedDb();
            }, 100)
        })
    }
    on_btnClick(e) {
        const id = e.target.id;
        if (id === 'settings') {
            this.showSets(e);
        } else if (id === 'add') {
            this.showCreateLocalDbDialog();
        }
    }
    async on_rowClick(e, idx, name) {
        this.dbSelectedIdx = idx;
        window.location.reload();
    }

    async on_rowRightClick(e, idx, dbItem) {
        e.stopPropagation();
        e.preventDefault();
        this.showLocalDbMenu(e, dbItem);
    }
    async getDB(name = this.combinedDbList[this.dbSelectedIdx]?.name, dbUrl = this.dbUrl, _name) {
        if (this.base) this.base.showLoader = true;
        const selectedDb = this.combinedDbList[this.dbSelectedIdx];
        if (selectedDb?.type === 'local') {
            try {
                if (!FX.PouchDB) {
                    await import('/fx/~/pouchdb/pouchdb-9.0.0.min.js');
                    FX.PouchDB = PouchDB;
                }
                this.dbLocal = new FX.PouchDB(name);
                if (selectedDb.syncUrl && selectedDb.autoSync) {
                    let dbs = await FX.purix(name, selectedDb.syncUrl);
                    this.dbRemote = dbs.remote;
                } else
                    this.dbRemote = null;
                this.updateLocalDbInfo();
            } catch (error) {
                console.error('Error connecting to local database:', error);
                if (this.base) this.base.showLoader = false;
            }
        } else {
            let dbs = await FX.purix(_name || name, dbUrl);
            this.dbLocal = dbs.local;
            this.dbRemote = dbs.remote;
        }
        await this.syncDb();
        if (this.base) {
            this.base.dbLocal = this.dbLocal;
            this.base.dbRemote = this.dbRemote;
            this.async(() => this.base.showLoader = false, 100);
        }
        this.fire('db-selected', { dbLocal: this.dbLocal });
        this.$update();
    }
    async syncDb(local = this.dbLocal, remote = this.dbRemote) {
        if (local && remote) {
            try {
                await this.dbLocal.replicate.from(this.dbRemote).on('complete', result => {
                    this.syncHandler = local.sync(remote, { live: true, retry: true })
                        .on('change', change => { /*console.log('change')*/ })
                        .on('paused', paused => { /*console.log('paused')*/ })
                        .on('error', error => { /*console.log('sync error')*/ });
                    // this.async(() => this.fire('db-selected', { dbLocal: this.dbLocal }));
                    if (this.base) this.async(() => this.base.showLoader = false, 100);
                    return true;
                }).on('error', error => { console.log('replicate error - ', error) })
            } catch (error) {
                console.error('Error setting up synchronization:', error);
                if (this.base) this.base.showLoader = false;
            }
        }
    }
    updateCombinedList() {
        const remoteWithType = (this.dbList || []).map(db => ({ ...db, type: 'remote' }));
        const localWithType = (this.localDbList || []).map(db => ({ ...db, type: 'local' }));
        this.combinedDbList = [...remoteWithType, ...localWithType];
        this.dbSelectedIdx = this.dbSelectedIdx < 0 || this.dbSelectedIdx > this.combinedDbList.length - 1 ? 0 : this.dbSelectedIdx;
        this.$update();
    }

    scrollToSelectedDb() {
        if (this.dbSelectedIdx >= 0 && this.combinedDbList.length > 0) {
            this.async(() => {
                const container = this.shadowRoot.querySelector('.container');
                if (!container) return;
                const dbItems = container.querySelectorAll('.db-cell');
                const selectedItem = dbItems[this.dbSelectedIdx];
                if (selectedItem) {
                    selectedItem.scrollIntoViewIfNeeded();
                }
            }, 50)
        }
    }

    static styles = [$styles]

    render() {
        return html`
            <div class="horizontal m2 p4 sticky panel br brr2 center" style="z-index: 99; top: 2px;">
                <div class="flex">databases</div>
                <fx-icon id="settings" class="but mr8" url="mdi:dots-vertical" scale=.8 an="btn" br="square" @click=${this.on_btnClick} title="settings"></fx-icon>
                <fx-icon class="but mr4" id="add" name="carbon:add" fill="blue" scale=1 an="btn" br="square" class="mr4" @click=${this.on_btnClick} title="Создать локальную базу"></fx-icon>
            </div>
            <div class="container vertical flex w100 h100 relative overflow-y">
                ${(this.combinedDbList || []).map((i, idx) => html`
                    <div class="db-cell vertical panel br brr2 m4 p4 pointer relative overflow-h fm ${i.type === 'local' ? 'local-db' : 'remote-db'}"
                         ?selected=${idx === this.dbSelectedIdx}
                         @click=${e => this.on_rowClick(e, idx, i.name)}
                         @contextmenu=${e => this.on_rowRightClick(e, idx, i)}
                         style="font-size: 14px;">
                        <div class="horizontal align relative">
                            <span class="tr fs mr4" style="min-width: 32px; max-width: 32px; text-align: right;">${(idx + 1 + '').padStart(3, '0')} - </span>
                            <div class="flex">${i.name || ''}</div>
                            ${i.type === 'local' ? html`<div class="fxs pointer" style="margin-top: -16px; color: ${i.syncUrl && i.autoSync ? 'red' : ''}" @click=${e => this.on_rowRightClick(e, idx, i)}>local</div>` : html``}
                        </div>
                        <div class="horizontal fxs left relative">
                            <div class="ml4 mt2 mr2">${i.size} mb - </div>
                            <div class="mt2 mr2">${i.doc_count}</div>     
                        </div>
                    </div>
                `)}
            </div>
        `
    }

    loadLocalDbs() {
        const saved = localStorage.getItem(`fx-local-dbs-${this.dbLocalPrefix}`);
        if (saved) {
            try {
                this.localDbList = JSON.parse(saved);
            } catch (e) {
                this.localDbList = [];
            }
        }
        this.updateCombinedList();
        // this.async(() => {
        //     this.scrollToSelectedDb();
        // }, 100)
    }
    saveLocalDbs() {
        localStorage.setItem(`fx-local-dbs-${this.dbLocalPrefix}`, JSON.stringify(this.localDbList));
    }
    async updateLocalDbInfo() {
        try {
            const info = await this.dbLocal.info();
            const localDb = this.localDbList.find(db => db.name === this.dbLocal.name);
            if (localDb) {
                localDb.doc_count = info.doc_count;
                localDb.size = ((info.disk_size || 0) / 1024 / 1024).toFixed(3);
                this.saveLocalDbs();
                this.updateCombinedList();
            }
        } catch (error) {
            console.error('Error updating local database information:', error);
        }
    }
    async createLocalDb(name, autoSync = false, syncUrl = this.dbLocalUrl, dbPrefix = this.dbLocalPrefix) {
        if (!name) return;
        name = dbPrefix + name.trim();
        const exists = this.combinedDbList.find(db => db.name === name);
        if (exists) {
            await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Warning', info1: 'A database with this name already exists', info2: 'База данных с таким именем уже существует' });
            return;
        }
        const localDb = {
            prefix: dbPrefix,
            name: name,
            label: name,
            type: 'local',
            syncUrl,
            autoSync,
            created: new Date().toISOString(),
            doc_count: 0,
            size: '0.000'
        }
        this.localDbList.push(localDb);
        this.saveLocalDbs();
        this.updateCombinedList();
        try {
            if (!FX.PouchDB) {
                await import('/fx/~/pouchdb/pouchdb-9.0.0.min.js');
                FX.PouchDB = PouchDB;
            }
            const db = new FX.PouchDB(name);
            if (syncUrl && autoSync) {
                this.syncDb();
            }
        } catch (error) {
            console.error('Error create local db: ', error);
        }
    }
    async deleteLocalDb(name) {
        try {
            this.localDbList = this.localDbList.filter(db => db.name !== name);
            const remoteDb = this.dbList.find(db => db.name === this.dbLocal.name);
            this.dbList = this.dbList.filter(db => db.name !== name);
            this.saveLocalDbs();
            if (FX.PouchDB) {
                const db = new FX.PouchDB(name);
                await db.destroy();
                if (remoteDb) {
                    this.updateCombinedList();
                    this.dbSelectedIdx = -1;
                    this.$update();
                } else {
                    this.dbSelectedIdx -= 1;
                    this.dbSelectedIdx = this.dbSelectedIdx < 0 ? 0 : this.dbSelectedIdx;
                    window.location.reload();
                }
            }
        } catch (error) {
            console.error('Error delete local db: ', error);
        }
    }
    async syncLocalDbNow(localName, syncUrl) {
        try {
            const dbs = await FX.purix(localName, syncUrl);
            if (dbs.local && dbs.remote) {
                await dbs.local.replicate.from(dbs.remote);
                await dbs.local.replicate.to(dbs.remote);
                this.updateLocalDbInfo(localName);
            }
        } catch (error) {
            console.error('Error sync: ', error);
        }
    }

    getApi(val = '') {
        val = val.trim();
        if (val) {
            try {
                let arr1 = val.split('://');
                let arr2 = arr1[1].split('@');
                let url = arr1[0] + '://' + arr2[1];
                let name = arr2[0]?.split(':')[0];
                let psw = arr2[0]?.split(':')[1];
                if (arr2?.[1] && url && name && psw) {
                    this.defaultLocalDB = url;
                    val = { url, name, psw }
                    let jcurix = FX.jcurix(val);
                    console.log(FX.zdurix(jcurix));
                    return {
                        url, jcurix
                    }
                }
            } catch (error) { }
        }
    }
    async showSets(_e) {
        let dbPrefix = '';
        const run = async (e, item) => {
            let id = item.id;
            if (id === 'setPrefix') {
                dbPrefix = item.value;
            }
            else if (id === 'api') {
                this.dbUrl = item.value;
            }
            else if (id === 'setApi') {    
                this.dbUrl = '';
                this.defaultDB = item.value;
                let res = this.getApi(item.value);
                if (res) {
                    this.dbUrl = res.jcurix;
                    this.defaultDB = res.url;
                }
                const grid =  document.querySelectorAll('fx-grid')[0];
                BS_UTILS.allItems({ items: grid.item }).map(i => {
                    if (i.id === 'setApi' && res?.url) {
                        i.value = res.url;
                        grid?.$update();
                    }
                    if (i.id === 'api') i.value = this.dbUrl;
                })
                this.async(() => {
                    grid?.$update();
                }, 20 )
            }
            else if (id === 'reload') {
                localStorage.setItem('fx-base.dbPrefix', dbPrefix);
                window.location.href = window.location.href.split('#')[0] + (dbPrefix ? ('#pr=' + dbPrefix) : '#');
                window.location.reload();
            }
            else if (id === 'open') {
                const url = window.location.href.split('#')[0] + (dbPrefix ? ('#pr=' + dbPrefix) : '#');
                window.open(url, '_blank');
            }
            else if (id === 'clear') {
                const res = await FX.showModal({ cancel: 'Cancel', ok: 'Ok', modal: '300, 160', info1: 'Clear localStorage to default ?', info2: 'Сбросить настройки баз данных к настройкам по умолчанию?' });
                if (res.detail === 'ok') {
                    this.resetAllDefault();
                    window.location.reload();
                }
            }
            else if (id === 'export') {
                this.exportDatabase();
            }
            else if (id === 'import') {
                await this.showImportDialog(e);
                this.dbSelectedIdx = this.combinedDbList.length - 1;
                window.location.reload();
            }
        }
        const item = [
            {
                id: 'reload', label: 'DBs settings', subLabel: 'Настройки баз данных', value: 'reload', is: 'button', run, expanded: true, items: [
                    { id: 'setPrefix', label: 'Databases prefix', subLabel: 'Префикс баз данных', is: 'list', dataList: this.dataList, value: this.dbPrefix, run },
                    { id: 'api', label: 'API remote db', subLabel: 'API удаленной базы данных', value: this.dbUrl },
                    { id: 'setApi', label: 'Url remote db', subLabel: 'Url удаленной базы данных', value: this.defaultDB, run },
                    { id: 'open', label: 'Open in new tab', subLabel: 'Открыть в новой вкладке', value: 'open', is: 'button', run }
                ]
            },
            {
                id: 'reload', label: 'Advanced settings', subLabel: 'Расширенные настройки', is: 'span', items: [
                    { id: 'clear', label: 'Сlear localStorage', subLabel: 'Очистить локальные установки', value: 'clear', is: 'button', run },
                    { id: 'export', label: 'Export db', subLabel: 'Экспорт базы данных', value: 'export', is: 'button', run },
                    { id: 'import', label: 'Import db', subLabel: 'Импорт базы данных', value: 'import', is: 'button', run },
                ]
            },
        ]
        await FX.showGrid({ id: this.id + '-sets', item, rowHeight: 32, hideSelected: true }, { parent: this.$qs('#settings'), label: this.dbSelected?.name || 'DBs settings', intersect: true, align: 'left', class: 'br', minWidth: 300, id: this.id + '-base-db-sets', draggable: true, resizable: false, btnCloseOnly: false });

    }
    async showCreateLocalDbDialog() {
        let dbName = '', syncUrl = this.dbLocalUrl, autoSync = false, dbPrefix = this.dbLocalPrefix;
        const run = async (e, item) => {
            const id = item.id;
            if (id === 'setPrefix') {
                this.dbLocalPrefix = dbPrefix = item.value;
                localStorage.setItem('fx-base.dbLocalPrefix', dbPrefix);
            } else if (id === 'setName') {
                dbName = item.value.trim();
            } else if (id === 'api') {
                this.dbLocalUrl = syncUrl = item.value;
            } else if (id === 'setApi') {
                this.dbLocalUrl = syncUrl = '';
                this.defaultLocalDB = item.value;
                let res = this.getApi(item.value);
                if (res) {
                    this.dbLocalUrl = syncUrl = res.jcurix;
                    this.defaultLocalDB = res.url;
                }
                const grid =  document.querySelectorAll('fx-grid')[0];
                BS_UTILS.allItems({ items: grid.item }).map(i => {
                    if (i.id === 'setApi' && res?.url) {
                        i.value = res.url;
                        grid?.$update();
                    }
                    if (i.id === 'api') i.value = this.dbLocalUrl;
                })
                this.async(() => {
                    grid?.$update();
                }, 20 )
            } else if (id === 'setAutoSync') {
                autoSync = item.value;
            } else if (id === 'create') {
                if (dbName) {
                    await this.createLocalDb(dbName, autoSync, syncUrl, dbPrefix);
                    const dialog = document.getElementById(this.id + '-create-local-db');
                    if (dialog && dialog.close) dialog.close();
                    this.dbSelectedIdx = this.combinedDbList.length - 1;
                    window.location.reload();
                } else {
                    await FX.showModal({ ok: 'Ok', modal: '300, 160', info1: 'Enter the database name', info2: 'Введите имя базы данных' });
                }
            }
        }
        const item = [
            {
                label: 'Create a local database', subLabel: 'Создать локальную базу данных', is: 'span', value: '', expanded: true, items: [
                    { id: 'setPrefix', label: 'Database prefix', subLabel: 'Префикс базы данных', value: this.dbLocalPrefix, run },
                    { id: 'setName', label: 'Database name', subLabel: 'Имя базы данных', value: dbName, run },
                    { id: 'api', label: 'API remote db', subLabel: 'API удаленной базы данных', value: this.dbLocalUrl },
                    { id: 'setApi', label: 'Url remote db', subLabel: 'Url удаленной базы данных', value: this.defaultLocalDB, run },
                    { id: 'setAutoSync', label: 'Auto synchronization', subLabel: 'Автоматическая синхронизация', value: autoSync, is: 'checkbox', run },
                    { id: 'create', label: 'Create local db', subLabel: 'Создать локальную базу', value: 'create', is: 'button', run }
                ]
            }
        ]
        await FX.showGrid({
            id: this.id + '-create-db',
            item,
            rowHeight: 32,
            hideSelected: true
        }, {
            parent: this.$qs('#add'),
            label: 'Create a local database',
            intersect: true,
            align: 'left',
            class: 'br',
            minWidth: 280,
            id: this.id + '-create-local-db',
            draggable: true,
            resizable: false,
            btnCloseOnly: false
        })
    }

    async showLocalDbMenu(e, dbItem) {
        let syncUrl = dbItem.syncUrl || '', autoSync = dbItem.autoSync || false;
        const run = async (e, item) => {
            const id = item.id;
            if (id === 'api') {
                this.dbLocalUrl = syncUrl = item.value;
            } else if (id === 'setAutoSync') {
                autoSync = item.value;
            } else if (id === 'save') {
                const localDb = this.localDbList.find(db => db.name === dbItem.name);
                if (localDb) {
                    localDb.syncUrl = syncUrl.trim();
                    localDb.autoSync = autoSync;
                    this.saveLocalDbs();
                    this.updateCombinedList();
                    if (autoSync && syncUrl.trim()) this.syncDb();
                    else if (this.syncHandler) this.syncHandler.cancel();
                }
                const menu = document.getElementById(this.id + '-local-db-menu');
                if (menu && menu.close) menu.close();
            } else if (id === 'deleteDb') {
                const res = await FX.showModal({ cancel: 'Cancel', ok: 'Ok', modal: '360, 160', label: 'Warning', info1: `Delete local db - ${dbItem.name}?`, info2: `Удалить локальную базу данных - ${dbItem.name}?` })
                if (res.detail === 'ok') {
                    await this.deleteLocalDb(dbItem.name);
                }
            } else if (id === 'syncNow') {
                if (syncUrl.trim()) {
                    await this.syncLocalDbNow(dbItem.name, syncUrl.trim());
                }
            } else if (id === 'exportDb') {
                const oldSelectedIdx = this.dbSelectedIdx;
                const dbIndex = this.combinedDbList.findIndex(db => db.name === dbItem.name);
                if (dbIndex !== -1) {
                    this.dbSelectedIdx = dbIndex;
                    await this.exportDatabase('all', []);
                    this.dbSelectedIdx = oldSelectedIdx;
                }
            } else if (id === 'setApi') {
                this.dbLocalUrl = syncUrl = '';
                this.defaultLocalDB = item.value;
                let res = this.getApi(item.value);
                if (res) {
                    this.dbLocalUrl = syncUrl = res.jcurix;
                    this.defaultLocalDB = res.url;
                }
                const grid =  document.querySelectorAll('fx-grid')[0];
                BS_UTILS.allItems({ items: grid.item }).map(i => {
                    if (i.id === 'setApi' && res?.url) {
                        i.value = res.url;
                        grid?.$update();
                    }
                    if (i.id === 'api') i.value = this.dbLocalUrl;
                })
                this.async(() => {
                    grid?.$update();
                }, 20 )
            }
        }
        const items = dbItem.type === 'local' ? [
            {
                label: 'Database settings', subLabel: 'Настройки базы данных', is: 'span', expanded: true, run, items: [
                    { id: 'api', label: 'API for synchronization', subLabel: 'API для синхронизации', value: syncUrl, run },
                    { id: 'setApi', label: 'Url remote db', subLabel: 'Url удаленной базы данных', value: this.defaultLocalDB, run },
                    { id: 'setAutoSync', label: 'Auto synchronization', subLabel: 'Автоматическая синхронизация', value: autoSync, is: 'checkbox', run },
                    { id: 'syncNow', label: 'Sync now', subLabel: 'Синхронизировать сейчас', value: 'syncNow', is: 'button', run },
                    { id: 'save', label: 'Save db settings', subLabel: 'Сохранить настройки базы данных', value: 'save', is: 'button', expanded: true, run },
                    { id: 'exportDb', label: 'Export database', subLabel: 'Экспортировать базу данных', value: 'exportDb', is: 'button', run },
                    { id: 'deleteDb', label: 'Delete database', subLabel: 'Удалить базу данных', value: 'deleteDb', is: 'button', run }
                ]
            }
        ] : [
            {
                label: 'Database settings', subLabel: 'Настройки базы данных', is: 'span', expanded: true, run, items: [
                    { id: 'exportDb', label: 'Export database', subLabel: 'Экспортировать базу данных', value: 'exportDb', is: 'button', run },
                    { id: 'deleteDb', label: 'Delete database', subLabel: 'Удалить базу данных', value: 'deleteDb', is: 'button', run }
                ]
            }
        ]
        await FX.showGrid({
            id: this.id + '-local-menu',
            item: items,
            rowHeight: 32,
            hideSelected: true
        }, {
            parent: e.target,
            label: `Database: ${dbItem.name}`,
            intersect: true,
            align: 'left',
            class: 'br',
            minWidth: 350,
            id: this.id + '-local-db-menu',
            draggable: true,
            resizable: false,
            btnCloseOnly: false
        })
    }

    async showImportDialog(e) {
        let targetDbName = '', selectedFile = null, prefix = this.dbLocalPrefix;
        const run = async (e, item) => {
            const id = item.id;
            if (id === 'setTargetDbName') {
                targetDbName = item.value.trim();
            } else if (id === 'selectFile') {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = (e) => {
                    selectedFile = e.target.files[0];
                    if (selectedFile) {
                        const fileItem = items[0].items.find(i => i.id === 'selectedFile');
                        if (fileItem) {
                            fileItem.value = selectedFile.name;
                            fileItem.label = `Selected file`;
                            fileItem.subLabel = `Выбранный файл`;
                        }
                        this.$update();
                    }
                }
                input.click();
            } else if (id === 'import') {
                if (!selectedFile) {
                    await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Please select a file', info2: 'Пожалуйста, выберите файл' });
                    return;
                }
                if (!targetDbName) {
                    await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Please enter database name', info2: 'Пожалуйста, введите имя базы данных' });
                    return;
                }
                await this.importDatabase(selectedFile, targetDbName, prefix);
            } else if (id === 'prefix') {
                prefix = item.value;
            }
        }
        const items = [
            {
                label: 'Import Database', subLabel: 'Импорт базы данных', is: 'span', expanded: true, items: [
                    { id: 'selectFile', label: 'Select JSON file', subLabel: 'Выберите JSON файл', value: 'selectFile', is: 'button', run },
                    { id: 'selectedFile', label: 'No file selected', subLabel: 'Файл не выбран', value: '', is: 'span' },
                    { id: 'prefix', label: 'Database prefix', subLabel: 'Префикс базы данных', value: prefix, run },
                    { id: 'setTargetDbName', label: 'New database name', subLabel: 'Имя новой базы данных', value: targetDbName, run },
                    { id: 'import', label: 'Import', subLabel: 'Импортировать', value: 'import', is: 'button', run }
                ]
            }
        ]
        await FX.showGrid({
            id: this.id + '-import',
            item: items,
            rowHeight: 32,
            hideSelected: true
        }, {
            parent: e.target,
            label: 'Import Database',
            intersect: true,
            align: 'left',
            class: 'br',
            minWidth: 320,
            id: this.id + '-import-dialog',
            draggable: true,
            resizable: false,
            btnCloseOnly: false
        })
    }
    async exportDatabase() {
        const selectedDb = this.combinedDbList[this.dbSelectedIdx];
        if (!selectedDb) {
            await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'No database selected', info2: 'Не выбрана база данных' });
            return;
        }
        try {
            let db = this.dbLocal;
            if (!db) {
                await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Database not available', info2: 'База данных недоступна' });
                return;
            }
            let docs = [];
            const result = await db.allDocs({ include_docs: true, attachments: true });
            docs = result.rows.map(row => row.doc);
            if (docs.length === 0) {
                await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Warning', info1: 'No documents to export', info2: 'Нет документов для экспорта' });
                return;
            }
            const exportData = {
                database: selectedDb.name,
                type: selectedDb.type,
                exportDate: new Date().toISOString(),
                totalDocs: docs.length,
                docs: docs
            };
            const blob = new Blob([JSON.stringify(exportData, null, 4)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${selectedDb.name}_export_${FX.dates().short}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Success', info1: `Exported ${docs.length} documents`, info2: `Экспортировано ${docs.length} документов` });
        } catch (error) {
            console.error('Export error:', error);
            await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Export failed', info2: 'Ошибка экспорта: ' + error.message });
        }
    }
    async importDatabase(file, targetDbName = '', prefix) {
        try {
            const text = await file.text();
            const importData = JSON.parse(text);
            if (!importData.docs || !Array.isArray(importData.docs)) {
                await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Invalid import file format', info2: 'Неверный формат файла импорта' });
                return;
            }
            let targetDb;
            if (!targetDbName) {
                await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Target database name required', info2: 'Требуется имя целевой базы данных' });
                return;
            }
            const fullDbName = (prefix || this.dbLocalPrefix) + targetDbName;
            const exists = this.combinedDbList.find(db => db.name === fullDbName);
            if (exists && prefix === this.dbLocalPrefix) {
                await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Warning', info1: 'Database already exists', info2: 'База данных уже существует' });
                return;
            }
            if (!FX.PouchDB) {
                await import('/fx/~/pouchdb/pouchdb-9.0.0.min.js');
                FX.PouchDB = PouchDB;
            }
            targetDb = new FX.PouchDB(fullDbName);
            const localDb = {
                prefix: prefix || this.dbLocalPrefix,
                name: fullDbName,
                label: fullDbName,
                type: 'local',
                syncUrl: this.dbLocalUrl,
                autoSync: false,
                created: new Date().toISOString(),
                doc_count: 0,
                size: '0.000'
            };
            this.localDbList.push(localDb);
            this.saveLocalDbs();
            this.updateCombinedList();
            if (!targetDb) {
                await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Target database not available', info2: 'Целевая база данных недоступна' });
                return;
            }
            let successCount = 0;
            let errorCount = 0;
            for (const doc of importData.docs) {
                try {
                    delete doc._rev;
                    await targetDb.put(doc);
                    successCount++;
                } catch (error) {
                    console.warn('Failed to import document:', doc._id, error);
                    errorCount++;
                }
            }
            await FX.showModal({
                ok: 'Ok',
                modal: '350, 180',
                label: 'Import Complete',
                info1: `Imported: ${successCount}, Failed: ${errorCount}`,
                info2: `Импортировано: ${successCount}, Ошибок: ${errorCount}`
            });
            this.updateCombinedList();
        } catch (error) {
            console.error('Import error:', error);
            await FX.showModal({ ok: 'Ok', modal: '300, 160', label: 'Error', info1: 'Import failed', info2: 'Ошибка импорта: ' + error.message });
        }
    }
})
