html {
    font-family: sans-serif;
}

.cursor-pointer {
    cursor: pointer;
}

svg.main_svg {
    width: 100%;
    height: 100%;
    background: #626b78;
    color: #626b78;
}

svg.main_svg text {
    fill: currentColor;
}

rect.card-female,
.card-female .card-body-rect,
.card-female .text-overflow-mask {
    fill: lightpink;
}

rect.card-male,
.card-male .card-body-rect,
.card-male .text-overflow-mask {
    fill: lightblue;
}

.card-genderless .card-body-rect,
.card-genderless .text-overflow-mask {
    fill: #7c8594;
}

.card_add .card-body-rect {
    fill: #626b78;
    stroke-width: 4px;
    stroke: #fff;
    cursor: pointer;
}

g.card_add text {
    fill: #fff;
}

.card-main {
    stroke: #000;
}

.card_image {
    cursor: pointer;
}

/* card_family_tree element */
.card_family_tree rect {
    transition: .3s;
}
.card_family_tree:hover rect {
    transform: scale(1.1);
}

/* card_family_tree element */
.card_add_relative {
    cursor: pointer;
    color: #fff;
    transition: .3s;
}
.card_add_relative circle {
    fill: rgba(0, 0, 0, 0);
}

.card_add_relative:hover {
    color: #209CEE;
}

/* pencil_icon element */
.card_edit.pencil_icon {
    color: #fff;
    transition: .3s;
}
.card_edit.pencil_icon:hover {
    color: #209CEE;
}

.card_info.info_icon {
    fill: #fff;
    transition: .3s;
}
.card_info.info_icon:hover {
    fill: #209CEE;
}

/* link element */
.card_break_link,
.link_upper,
.link_lower,
.link_particles {
    transform-origin: 50% 50%;
    transition: 1s;
}

.card_break_link {
    color: #fff;
}

.card_break_link.closed .link_upper {
    transform: translate(-140.5px, 655.6px);
}

.card_break_link.closed .link_upper g {
    transform: rotate(-58deg);
}

.card_break_link.closed .link_particles {
    transform: scale(0);
}

/* create-tree styles */
.input-field input {
    height: 2.5rem !important;
}

.input-field>label:not(.label-icon).active {
    -webkit-transform: translateY(-8px) scale(0.8);
    transform: translateY(-8px) scale(0.8);
}