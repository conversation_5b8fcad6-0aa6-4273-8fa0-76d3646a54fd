import f3 from '/fx/~/family-chart/index.js'
import { Form } from "/fx/~/family-chart/view/elements/Form.js"


(async () => {
    const cont = document.querySelector("#FamilyChart"),
        card_dim = { w: 360, h: 100, text_x: 80, text_y: 15, img_w: 60, img_h: 90, img_x: 5, img_y: 5 },
        card_display = cardDisplay(),
        card_edit = cardEditParams(),
        store = f3.createStore({
            data: firstNode(),
            node_separation: 400,
            level_separation: 150
        }),
        view = f3.d3AnimationView({
            store,
            cont: document.querySelector("#FamilyChart"),
            card_edit,
        }),
        Card = f3.elements.Card({
            store,
            svg: view.svg,
            card_dim,
            card_display,
            mini_tree: true,
            link_break: false,
            cardEditForm,
            addRelative: f3.handlers.AddRelative({ store, cont, card_dim, cardEditForm, labels: { mother: 'Добавить мать' } }),
        }),
        onUpdate = (props) => {
            view.update(props || {});
            document.dispatchEvent(new CustomEvent("changed", {
                detail: store.getData(),
                bubbles: true,
                cancelable: true,
            }))
        }
    view.setCard(Card)
    store.setOnUpdate(onUpdate)
    store.update.tree({ initial: true })

    function cardEditForm(props) {
        const postSubmit = props.postSubmit;
        props.postSubmit = (ps_props) => { postSubmit(ps_props) }
        const el = document.querySelector('#form_modal'),
            modal = M.Modal.getInstance(el),
            edit = { el, open: () => modal.open(), close: () => modal.close() }
        Form({ ...props, card_edit, card_display, edit })
    }
    top.store = document.store = store;
})()

function firstNode() {
    return [{ id: '0', rels: {}, data: {} }]
}

function cardEditParams() {
    return [
        { type: 'text', placeholder: 'ФИО', key: 'label', itype: 'text' },
        { type: 'text', placeholder: 'даты', key: 'desc', itype: 'text' },
        { type: 'text', placeholder: 'дополнительная информация', key: 'info', itype: 'text' },
        { type: 'text', placeholder: 'фото (ссылка или base64)', key: 'avatar', itype: 'text' }
    ]
}

function cardDisplay() {
    const d1 = d => `${d.data['label'] || ''}`,
        d2 = d => `${d.data['desc'] || ''}`,
        d3 = d => `${d.data['info'] || ''}`
    d1.create_form = "{label}"
    d2.create_form = "{desc}"
    d3.create_form = "{info}"
    return [d1, d2, d3]
}