# Исправление проблемы с функциями в редакторах шаблонов

## Проблема

При редактировании шаблонов таблиц функции (`calc`, `customFn` и другие) не попадали в редактор, потому что:
- `JSON.stringify()` не может сериализовать функции
- Функции терялись при преобразовании в JSON/JavaScript код
- После редактирования функциональные поля не работали

## Решение

Реализована универсальная обработка всех функций в обоих редакторах:
- Определение функций по наличию `=>` или `function` в строке
- Обработка всех свойств объектов, а не только конкретных полей
- Поддержка `calc`, `customFn` и любых других функциональных полей

### 1. JSON редактор (`showTemplateEditor`)

#### При создании шаблона для редактирования:
```javascript
// Универсальная обработка колонок для сохранения всех функций
const processedColumns = (this.columns || []).map(col => {
    const processedCol = { ...col };
    // Преобразуем все функции в строки
    Object.keys(processedCol).forEach(key => {
        if (typeof processedCol[key] === 'function') {
            processedCol[key] = processedCol[key].toString();
        }
    });
    return processedCol;
});
```

#### При применении изменений:
```javascript
// Универсальная обработка колонок для восстановления всех функций
this.columns = parsedData.columns.map(col => {
    const processedCol = { ...col };
    // Проверяем все свойства колонки на наличие функций
    Object.keys(processedCol).forEach(key => {
        const value = processedCol[key];
        if (typeof value === 'string' && value.trim()) {
            try {
                // Определяем функцию по наличию => или function
                if (value.includes('=>') || value.startsWith('function')) {
                    processedCol[key] = eval(`(${value})`);
                }
            } catch (error) {
                console.warn(`Failed to parse function in ${key}:`, value, error);
                // Оставляем как строку, если не удалось преобразовать
            }
        }
    });
    return processedCol;
});
```

### 2. JavaScript редактор (`showTemplateEditorJS`)

#### При создании шаблона для редактирования:
```javascript
// Специальная сериализация для сохранения функций
const serializeWithFunctions = (obj) => {
    return JSON.stringify(obj, (key, value) => {
        if (typeof value === 'function') {
            return value.toString();
        }
        return value;
    }, 4);
};

jsSource = `export default ${serializeWithFunctions(templateData)};`;
```

#### При применении изменений:
```javascript
// Специальная обработка колонок для восстановления calc функций
if (parsedData.columns) {
    this.columns = parsedData.columns.map(col => {
        const processedCol = { ...col };
        // Преобразуем строку calc обратно в функцию
        if (typeof processedCol.calc === 'string' && processedCol.calc.trim()) {
            try {
                if (processedCol.calc.includes('=>') || processedCol.calc.startsWith('function')) {
                    processedCol.calc = eval(`(${processedCol.calc})`);
                }
            } catch (error) {
                console.warn('Failed to parse calc function:', processedCol.calc, error);
            }
        }
        return processedCol;
    });
}
```

## Поддерживаемые типы функций

### calc функции (вычисления в колонках):
```javascript
calc: (e) => (e.qty || 0) * (e.price || 0)
calc: (e) => Math.round(e.total * 0.2 * 100) / 100
calc: function(e) { return e.qty * e.price; }
```

### customFn функции (пользовательские вычисления в футере):
```javascript
customFn: (data) => `Total: ${data.length} items`
customFn: (data) => data.reduce((sum, item) => sum + item.price, 0)
customFn: function(data) { return `Count: ${data.filter(item => item.active).length}`; }
```

### Любые другие функциональные поля:
```javascript
validator: (value) => value > 0
formatter: (value) => value.toFixed(2)
onClick: (e, item) => console.log('Clicked:', item)
```

### Сложные функции:
```javascript
calc: (e) => {
    const total = (e.qty || 0) * (e.price || 0);
    const tax = Math.round(total * 0.2 * 100) / 100;
    return Math.round((total + tax) * 100) / 100;
}
```

## Примеры в редакторах

### JSON редактор покажет:
```json
{
    "field": "total",
    "header": "Итого",
    "calc": "(e) => (e.qty || 0) * (e.price || 0)"
}
```

### JavaScript редактор покажет:
```javascript
{
    "field": "total",
    "header": "Итого", 
    "calc": "(e) => (e.qty || 0) * (e.price || 0)"
}
```

## Безопасность

- Используется `eval()` только для проверенных строк функций
- Проверяется наличие `=>` или `function` в строке
- При ошибке парсинга функция остается как строка
- Логируются предупреждения о неудачном парсинге

## Тестирование

Добавлен специальный тест `testCalcFields()` в `test-smart-editors.html`:
- Создает таблицу с различными типами calc функций
- Простые вычисления: `(e) => (e.qty || 0) * (e.price || 0)`
- Сложные вычисления с Math функциями
- Многострочные функции с блоками кода

## Результат

Теперь все функции (`calc`, `customFn`, и любые другие):
✅ Автоматически определяются по наличию `=>` или `function` в строке
✅ Сохраняются при редактировании в JSON формате
✅ Сохраняются при редактировании в JavaScript формате
✅ Корректно восстанавливаются после применения изменений
✅ Работают во всех поддерживаемых форматах
✅ Безопасно обрабатываются с проверкой ошибок
✅ Поддерживают любые функциональные поля, не только `calc`

Проблема полностью решена - все функциональные поля теперь корректно отображаются и редактируются в обоих типах редакторов шаблонов, независимо от их названия.
