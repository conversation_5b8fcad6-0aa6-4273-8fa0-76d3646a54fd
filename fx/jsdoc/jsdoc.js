import {FxJComp } from '../jcomp/jcomp.js';

const libPath = import.meta.url.split('/').slice(0, -1).join('/') + '/lib/';

customElements.define('fx-jsdoc', class FxJsdoc extends FxJComp {
    firstUpdated() {
        super.firstUpdated();
        this.useIframe = true;
    }

    srcdoc(src = this.checkedURL, editMode = this.editMode) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
  <link rel="stylesheet" href="${libPath}style.css">
  <script type="module" src="${libPath}superdoc.umd.js"></script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SuperDoc Editor</title>
  <style>
    body {
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        height: 100vh;
        background: white;
    }
    .superdoc__layers.layers {
        margin: 8px auto;
    }
  </style>
</head>
<body>
  <div id="my-toolbar" style="border-bottom: 1px solid lightgray;"></div>
  <div style="overflow: hidden; overflow-y: auto;">
        <div id="superdoc"></div>
  </div>

<script type="module">
    let superdoc = null;
    let isReady = false;
    let editor;

    const config = {
        selector: '#superdoc',
        toolbar: '${ editMode ? '#my-toolbar' : '' }',
        document: '${src}',
        documentMode: '${ editMode ? 'editing' : 'viewing' }',
        pagination: true,
        rulers: ${ editMode ? true : false },
        onReady: (event) => {
            isReady = true;
        },
        onEditorCreate: (event) => {
            editor = event.editor;
        },
        onEditorUpdate: async (event) => {
            const docx = await event.editor.exportDocx({ getUpdatedDocs: true });
            document.dispatchEvent(new CustomEvent('change', { detail: docx }));
        }
    }
    superdoc = new SuperDocLibrary.SuperDoc(config);

</script>

</body>
</html>
        `
    }
})
