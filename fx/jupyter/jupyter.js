import { FxElement, html, css } from '/fx.js';
import { $styles, $stylesCell } from './jupyter.x.js';
import '../button/button.js';
import '../splitter/splitter.js';
import '../rating/rating.js';

export class FxJupyter extends FxElement {
    static properties = {
        base: { type: Object },
        notebook: { type: Object, notify: true },
        showBorder: { type: Boolean, default: false },
        showCellType: { type: Boolean, default: false, save: true },
        allwaysShowToolbar: { type: <PERSON><PERSON>an, default: false, save: true },
        selectedIdx: { type: Number, default: -1 }
    }
    get cells() { return this._cells || this.notebook?.cells || [] }
    get fxCells() { return this.$qsa('fx-jupyter-cell') }

    'notebook-changed'(e) {
        // console.log(e);
        FX.closeForm(this.base.id + '-cell-sets-dd');
        this._cells = [];
        this.async(() => {
            this._cells = undefined;
            this.$update();
        }, 100)
    }
    changeLabel(e) {
        this.base.fxFlat[this.base.fxSelected._id].tags = this.notebook.label = e.target.value;

        this.$update();
    }
    changeRating(e) {
        this.notebook.rating = e.detail;
        this.$update();
    }

    createNewCell(cellType = 'link') {
        return {
            ulid: FX.ulid(),
            type: 'jupyter_cell',
            cell_type: cellType,
            cell_extType: cellType,
            source: '',
            h: 24,
            minH: 24,
        }
    }
    addCellTop(currentIdx = this.selectedIdx) {
        if (!this.notebook.cells) this.notebook.cells = [];
        const newCell = this.createNewCell();
        this.notebook.cells.splice(currentIdx, 0, newCell);
        this.selectedIdx = currentIdx + 1;
        this.$update();
    }
    addCellBottom(currentIdx = this.selectedIdx) {
        if (!this.notebook.cells) this.notebook.cells = [];
        const newCell = this.createNewCell();
        this.notebook.cells.splice(currentIdx + 1, 0, newCell);
        this.selectedIdx = currentIdx;
        this.$update();
    }
    deleteCell(currentIdx = this.selectedIdx) {
        if (!this.notebook.cells || currentIdx < 0 || currentIdx >= this.notebook.cells.length) return;
        this.base.toDeleteKeys ||= [];
        this.base.toDeleteKeys.push(this.notebook.cells[currentIdx]._idFile);
        this.notebook.cells.splice(currentIdx, 1);
        this.selectedIdx = currentIdx - 1;
        this.selectedIdx = this.selectedIdx < 0 ? 0 : this.selectedIdx;
        if (!this.notebook.cells?.length) {
            this.base.toDeleteKeys.push(this.notebook._id);
            this.notebook._deleted = true;
        }
        FX.closeDD();
        this.$update();
    }
    moveCellUp(currentIdx = this.selectedIdx) {
        if (!this.notebook.cells || currentIdx <= 0 || currentIdx >= this.notebook.cells.length) return;
        const cell = this.notebook.cells[currentIdx];
        this.notebook.cells.splice(currentIdx, 1);
        this.notebook.cells.splice(currentIdx - 1, 0, cell);
        this.selectedIdx = currentIdx - 1;
        this.selectedIdx = this.selectedIdx < 0 ? 0 : this.selectedIdx;
        this.$update();
    }
    moveCellDown(currentIdx = this.selectedIdx) {
        if (!this.notebook.cells || currentIdx < 0 || currentIdx >= this.notebook.cells.length - 1) return;
        const cell = this.notebook.cells[currentIdx];
        this.notebook.cells.splice(currentIdx, 1);
        this.notebook.cells.splice(currentIdx + 1, 0, cell);
        this.selectedIdx = currentIdx + 1;
        this.$update();
    }

    static styles = [$styles]

    render() {
        return html`
            <div class="vertical flex w100 h100 relative box overflow-h">
                <div class="panel horizontal w100 relative align mmh32 pl6 pr6 brbl" style="z-index: 99" ?hidden=${!this.notebook || this.notebook._deleted}>
                    <fx-icon class="ml4" url=${this.notebook?.icon} .hidden=${!this.notebook?.icon}></fx-icon>
                    <input class="inp tr fm ellipsis flex" .value=${this.notebook?.label || this.notebook?.$label || ''} @change=${this.changeLabel} ?disabled=${this.disabledEditLabel} placeholder="tags...">
                    <fx-rating size=18 .value=${this.notebook?.rating || 0} @change=${this.changeRating}></fx-rating>
                </div>
                <div class="toolbar-toggle absolute" style="height: 4px; cursor: pointer; width: 100%; position: relative; display: flex; justify-content: end; align-items: center; z-index: 10; top: 4px;" @click=${this.addFirstCell} ?hidden=${this.cells?.length || !this.base?.fxSelected}>
                    <div class="btn-tool" selected></div>
                </div>
                <div class="vertical w100 relative box pl8 pr8 mt4 ${!this.cells || this.cells?.length <= 1 ? 'h100 overflow-h' : 'overflow-y'}" style="margin-top: ${this.showCellType ? 'unset' : '-6px'};">
                    ${(this.cells).map((cell, idx) => html`
                        <fx-jupyter-cell class="mt8" .jupyter=${this} .cell=${cell} idx=${idx} .notebook=${this.notebook} level=${this.level} .base=${this.base}></fx-jupyter-cell>
                    `)}
                    <div style="display: block; min-height: 90vh;" ?hidden=${this.cells?.length === 1}></div>
                </div>
            </div>
        `
    }

    async addFirstCell(e) {
        const run = async (e, item) => {
            this.async(() => {
                this.notebook.cells = [];
                const newCell = this.createNewCell(item.value);
                this.notebook.cells.push(newCell);
                FX.closeDD();
                this.$update();
                if (item.value === 'link' || item.value === 'html' || item.value === 'cherry' || item.value === 'svg') {
                    this.async(() => this.fxCells[0].toolbarVisible = true, 500);
                }
            })
        }
        const item = [
            { icon: 'flat-color-icons:multiple-smartphones:28', label: 'link', value: 'link', is: 'button', run },
            { icon: 'vscode-icons:file-type-html:28', label: 'html', value: 'html', is: 'button', run },
            { icon: 'emojione-v1:cherries:28', label: 'cherry-md', value: 'cherry-md', is: 'button', run },
            { icon: 'vscode-icons:file-type-word:28', label: 'doc', value: 'doc', is: 'button', run },
            { icon: 'fc:multiple-cameras:28', label: 'galleries', value: 'galleries', is: 'button', run },
            { icon: 'fc:todo-list:28', label: 'to-do', value: 'to-do', is: 'button', run },
            {
                icon: 'fluent-color:code-block-16:28', label: 'code', items: [
                    { icon: 'devicon:vscode:28', label: 'code', value: 'code', is: 'button', run },
                    { icon: 'devicon:reactnative:28', label: 'executable', value: 'executable', is: 'button', run },
                    { icon: 'flat-color-icons:process:28', label: 'diff', value: 'diff', is: 'button', run },
                ]
            },
            {
                icon: 'devicon-original:apachespark:28', label: 'special', items: [
                    { icon: 'flat-color-icons:kindle:28', label: 'txt', value: 'txt', is: 'button', run },
                    { icon: 'devicon-original:apachespark:28', label: 'svg', value: 'svg', is: 'button', run },
                    { icon: 'devicon-original:gitbook:28', label: 'excalidraw', value: 'excalidraw', is: 'button', run },
                    { icon: 'fc:currency-exchange:28', label: 'money', value: 'money', is: 'button', run },
                    { icon: 'fc:conference-call:28', label: 'family-tree', value: 'family-tree', is: 'button', run },
                ]
            },
            {
                icon: 'fc:grid:28', label: 'table', items: [
                    { icon: 'flat-color-icons:view-details:28', label: 'table', value: 'table', is: 'button', run },
                    { icon: 'fc:grid:28', label: 'jspreadsheet', value: 'jspreadsheet', is: 'button', run },
                    { icon: 'fc:data-sheet:28', label: 'spreadsheet', value: 'spreadsheet', is: 'button', run },
                ]
            }
        ]
        await FX.showGrid({ type: 'add-new-cell', id: this.base.id + '-add-new-cell', item, rowHeight: 32, hideSelected: true }, { id: this.base.id + '-add-new-cell-dd', label: 'add new cell', parent: e.target, intersect: true, align: 'left', class: 'br', minWidth: 280, draggable: true, resizable: false, btnCloseOnly: false }, true);
        return;
    }
}
customElements.define('fx-jupyter', FxJupyter);

export class FxJupyterCell extends FxElement {
    static properties = {
        base: { type: Object },
        jupyter: { type: Object },
        notebook: { type: Object },
        cell: { type: Object },
        idx: { type: Number },
        level: { type: Number },
        selected: { type: Object },
        hide: { type: String, default: '' },
        toolbarVisible: { type: Boolean, default: false },
        editMode: { type: Boolean, default: false }
    }
    get type() {
        let type = this.cell?.cell_type || this.cell?.cell_extType;
        if (type === 'link' || (type === 'html' && this.isLoad) || type === 'epub') {
            import('../jlink/jlink.js');
            this.hide = 'e';
            return 'link';
        }
        if (type === 'iframe' || type === 'html' || type === 'suneditor' || type === 'html-suneditor' || type === 'ckeditor-4' || type === 'cke' || type === 'html-cke' || type === 'ckeditor-5' || type === 'ckeditor' || type === 'html-ckeditor' || type === 'jodit' || type === 'html-jodit') {
            this.hide = 'uad';
            this.editType = 'html';
            import('../jhtml/jhtml.js');
            return 'iframe';
        }
        if (type === 'cherry-md' || type === 'cherry' || type === 'cherrymd') {
            import('../jcherry/jcherry.js');
            this.hide = 'uad';
            return 'cherry';
        }
        if (type === 'md' || type === 'markdown') {
            import('../jmd/jmd.js');
            this.hide = 'uad';
            return 'md';
        }
        if (type === 'gallery' || type === 'galleries') {
            import('../jgallery/jgallery.js');
            this.hide = 'uaoed'
            return 'gallery';
        }
        if (type === 'tmp') {
            import('../jtmp/jtmp.js');
            this.hide = 'uaoed';
            return 'tmp';
        }
        if (type === 'to-do' || type === 'todo') {
            import('../jtodo/jtodo.js');
            this.hide = 'uaoed';
            return 'todo';
        }
        if (type === 'table') {
            import('../table/table.js');
            this.hide = 'uaoed';
            return 'table';
        }
        if (type === 'code') {
            import('../jcode/jcode.js');
            this.hide = 'uaoed';
            return 'code';
        }
        if (type === 'executable' || type === 'exe') {
            import('../jexe/jexe.js');
            this.hide = 'uaoed';
            return 'exe';
        }
        if (type === 'jsheet' || type === 'jspreadsheet') {
            import('../jsheet/jsheet.js');
            this.hide = 'uaoed';
            return 'sheet';
        }
        if (type === 'jspreadsheet' || type === 'spreadsheet' || type === 'spread') {
            import('../jspreadsheet/jspreadsheet.js');
            this.hide = 'uaoed';
            return 'spreadsheet';
        }
        if (type === 'svg') {
            import('../jsvg/jsvg.js');
            this.hide = 'uaod';
            return 'svg';
        }
        if (type === 'excalidraw') {
            import('../jexcalidraw/jexcalidraw.js');
            this.hide = 'uaoed';
            return 'excalidraw';
        }
        if (type === 'money') {
            import('../jmoney/jmoney.js');
            this.hide = 'uaoed';
            return 'money';
        }
        if (type === 'family-tree' || type === 'jfamily' || type === 'family' || type === 'familytree') {
            import('../jfamily/jfamily.js');
            this.hide = 'uaoed';
            return 'family';
        }
        if (type === 'diff') {
            import('../jdiff/jdiff.js');
            this.hide = 'uaoed';
            return 'diff';
        }
        if (type === 'txt') {
            this.hide = 'uaoed';
            return 'txt';
        }
        if (type === 'doc' || type === 'docx' || type === 'document') {
            import('../jsdoc/jsdoc.js');
            this.hide = '';
            return 'doc';
        }
        return type;
    }
    get selected() { return this.jupyter?.selectedIdx === this.idx }
    get showBorder() { return this.cell?.showBorder || this.jupyter?.showBorder }
    get hideLink() { return this.hide?.includes('l') }
    get hideUrl() { return this.hide?.includes('u') }
    get hideAdd() { return this.hide?.includes('a') }
    get hidePdf() { return this.hide?.includes('p') }
    get hideSets() { return this.hide?.includes('s') }
    get hideDelete() { return this.hide?.includes('d') }
    get hideOpen() { return this.hide?.includes('o') }
    get hideTop() { return this.hide?.includes('t') }
    get hideEdit() { return this.hide?.includes('e') }
    get hideSize() { return this.hide?.includes('z') }
    get isLoad() { return this.cell?.isLoad || false }

    _btnClick() { }
    async btnClick(e) {
        const res = this._btnClick(e) || 'run';
        const id = e.target.id;
        //console.log(res, id);
        if (id === 'delete') {
            this.ctrl?.deleteFile(true);
            return;
        }
        if (id === 'open') {
            let url = this.ctrl?.checkedURL || this.ctrl?.checkedUrl || '';
            if (url)
                window.open(url, '_blank').focus();
            return;
        }
        if (id === 'edit') {
            this.editMode = !this.editMode;
            this.$update();
            return;
        }
        if (id === 'settings') {
            const run = (e, item) => {
                this.cell.cell_type = this.cell.cell_extType = item.label;
                this.$update();
            }
            const item = [
                {
                    icon: 'flat-color-icons:accept-database:28', label: 'set cell info type', subLabel: 'установить тип ячейки', expanded: false, items: [
                        { icon: 'flat-color-icons:multiple-smartphones:28', label: 'link', value: 'link', is: 'button', run },
                        { icon: 'vscode-icons:file-type-html:28', label: 'html', value: 'html', is: 'button', run },
                        { icon: 'emojione-v1:cherries:28', label: 'cherry-md', value: 'cherry-md', is: 'button', run },
                        { icon: 'fc:multiple-cameras:28', label: 'galleries', value: 'galleries', is: 'button', run },
                        { icon: 'fc:todo-list:28', label: 'to-do', value: 'to-do', is: 'button', run },
                        {
                            icon: 'fluent-color:code-block-16:28', label: 'code', items: [
                                { icon: 'devicon:vscode:28', label: 'code', value: 'code', is: 'button', run },
                                { icon: 'devicon:reactnative:28', label: 'executable', value: 'executable', is: 'button', run },
                                { icon: 'flat-color-icons:process:28', label: 'diff', value: 'diff', is: 'button', run },
                            ]
                        },
                        {
                            icon: 'devicon-original:apachespark:28', label: 'special', items: [
                                { icon: 'flat-color-icons:kindle:28', label: 'txt', value: 'txt', is: 'button', run },
                                { icon: 'vscode-icons:file-type-word:28', label: 'doc', value: 'doc', is: 'button', run },
                                { icon: 'devicon-original:apachespark:28', label: 'svg', value: 'svg', is: 'button', run },
                                { icon: 'devicon-original:gitbook:28', label: 'excalidraw', value: 'excalidraw', is: 'button', run },
                                { icon: 'fc:currency-exchange:28', label: 'money', value: 'money', is: 'button', run },
                                { icon: 'fc:conference-call:28', label: 'family-tree', value: 'family-tree', is: 'button', run },
                            ]
                        },
                        {
                            icon: 'fc:grid:28', label: 'table', items: [
                                { icon: 'flat-color-icons:view-details:28', label: 'table', value: 'table', is: 'button', run },
                                { icon: 'fc:grid:28', label: 'jspreadsheet', value: 'jspreadsheet', is: 'button', run },
                                { icon: 'fc:data-sheet:28', label: 'spreadsheet', value: 'spreadsheet', is: 'button', run },
                            ]
                        },
                    ]
                },
                {
                    icon: 'lsicon:move-filled:28', label: 'cell actions', subLabel: 'действия с ячейкой', expanded: false, items: [
                        { icon: 'akar-icons:align-to-top:28', label: 'add new - top', subLabel: 'добавить сверху', value: 'add-top', is: 'button', run: () => this.jupyter.addCellTop() },
                        { icon: 'akar-icons:align-to-bottom:28', label: 'add new - bottom', subLabel: 'добавить снизу', value: 'add-bottom', is: 'button', run: () => this.jupyter.addCellBottom() },
                        { icon: 'lsicon:move-up-filled:28', label: 'move cell - up', subLabel: 'переместить вверх', value: 'move-up', is: 'button', run: () => this.jupyter.moveCellUp() },
                        { icon: 'lsicon:move-down-filled:28', label: 'move cell - down', subLabel: 'переместить вниз', value: 'move-down', is: 'button', run: () => this.jupyter.moveCellDown() },
                        { icon: 'carbon:close:32', fill: 'red', label: 'delete cell', subLabel: 'удалить ячейку', value: 'delete-cell', is: 'button', run: () => this.jupyter.deleteCell() },
                    ]
                },
                {
                    icon: 'emojione:artist-palette:28', label: 'current cell style', subLabel: 'стилизировать ячейку', expanded: false, items: [
                        { icon: 'vscode-icons:file-type-style:28', label: 'cell class', subLabel: 'класс ячейки', value: this.cell.class || '', run: (e) => { this.cell.class = e.target.value; this.$update() } },
                        { icon: 'fluent-color:text-edit-style-16:28', label: 'cell style', subLabel: 'стиль ячейки', value: this.cell.style || '', run: (e) => { this.cell.style = e.target.value; this.$update() } },
                    ]
                },
                {
                    icon: 'flat-color-icons:settings:28', label: 'cells sets', subLabel: 'настройки ячеек', expanded: false, items: [
                        { icon: 'material-symbols-light:toolbar-outline:28', subLabel: 'показывать тулбар', label: 'show toolbar', value: this.jupyter.allwaysShowToolbar, is: 'checkbox', run: () => { this.jupyter.allwaysShowToolbar = !this.jupyter.allwaysShowToolbar; this.$update() } },
                        { icon: 'material-symbols-light:password-rounded:28', subLabel: 'показывать тип ячейки', label: 'show cells type', value: this.jupyter.showCellType, is: 'checkbox', run: () => { this.jupyter.showCellType = !this.jupyter.showCellType; this.$update() } },
                        { icon: 'material-symbols-light:border-outer-rounded:28', subLabel: 'показывать границу', label: 'show all border', value: this.jupyter.showBorder, is: 'checkbox', run: () => { this.jupyter.showBorder = !this.jupyter.showBorder; this.$update() } },
                        { icon: 'material-symbols-light:border-clear-rounded:28', subLabel: 'скрывать границу у текущей', label: 'hide current border', value: this.cell.hideBorder, is: 'checkbox', run: () => { this.cell.hideBorder = !this.cell.hideBorder; this.$update() } },
                    ]
                }
            ]
            this.jupyter.selectedIdx = this.idx;
            this.$update();
            await FX.showGrid({ type: 'cell-sets', id: this.base.id + '-cell-sets', item, rowHeight: 32, hideSelected: true }, { id: this.base.id + '-cell-sets-dd', label: this.cell.cell_type + ' cell - ' + this.idx, parent: e.target, intersect: true, align: 'left', class: 'br', minWidth: 360, draggable: true, resizable: false, btnCloseOnly: false }, true);
            this.jupyter.selectedIdx = -1;
            this.$update();
            return;
        }
        if (id === 'pdf' && this.ctrl) {
            this.ctrl.pdfjsView = !this.ctrl.pdfjsView;
            this.$update();
            return;
        }
        if (id === 'load') {
            this.cell.isLoad = true;
            if (this.type === 'doc' && this.ctrl) {
                const file = e.target.files[0];
                if (file) {
                    this.ctrl.loadFile(file);
                }
            } else {
                this.ctrl?.loadFile(e);
            }
            return;
        }
        if (id === 'links') {
            let links = this.cell?.links || '';
            links = links.split(' ');
            links = links.filter(i => i);
            links.map(i => {
                window.open(i.trim(), '_blank');
            })
            return;
        }
    }
    changeTop(e) {
        const id = e.target.id;
        this[id] = e.target.value;
        if (this.cell) {
            this.cell[id] = this[id];
        }
        this.$update();
    }
    splitterResize(e) {
        this.h = e.detail.height;
        if (this.cell) {
            this.cell.cell_h = this.cell.h = this.h;
        }
        this.$update();
    }

    static styles = [$stylesCell]

    get getTop() {
        if (this.hideTop) return null;
        let url = this.cell?.url || this.url || '';
        return html`
            <div class="horizontal flex align p2 mt2 mmh32" style="border: 1px solid var(--fx-border-color-light);">
                <input id="labelLink" class="flex inp mr6 pl2 ml1" .value=${this.cell?.labelLink || this.labelLink || ''} @change=${this.changeTop} title=${this.cell?.labelLink || this.labelLink || ''}>
                <label class="pl4 mr2" ?hidden=${this.hideLink}>links:</label>
                <input id="links" class="flex ml2 inp mr4 pl2" .value=${this.cell?.links || this.links || ''} ?hidden=${this.hideLink} style="max-width: 360px;" @change=${this.changeTop} title=${this.cell?.links || this.links || ''}>
                <fx-icon id="links" url="fxemoji:meridianglobe" class="ml4 mr4" size="22" an="btn" scale=".9" title="open link in new tab" ?hidden=${this.hideLink} @click=${this.btnClick} style="opacity: ${(this.cell?.links || this.links || '').length ? 1 : .3}"></fx-icon>
                <label class="ml4 mr2" ?hidden=${this.hideUrl}>url:</label>
                <input id="url" class="flex ml2 inp pl2" @change=${this.changeTop} .value=${url} ?hidden=${this.hideUrl} style="max-width: 360px;" title=${url}>
                <fx-icon id="pdf" url="carbon:document-pdf" class="ml8 mr4" size="24" an="btn" scale=".9" @click=${this.btnClick} title="pdf type" ?hidden=${!this.ctrl || !this.ctrl?.is_pdf || this.hidePdf} fill=${this.ctrl?.pdfjsView ? 'red' : ''}></fx-icon>
                <fx-icon id="open" url="bs:share" class="ml4 mr4" size="20" an="btn" scale=".9" @click=${this.btnClick} title="open in new tab" ?hidden=${this.hideOpen}></fx-icon>
                <fx-icon id="settings" class="but ml4 mr4" url="mdi:dots-vertical:20" scale=.8 an="btn" br="square" @click=${this.btnClick} ?hidden=${this.hideSets} title="settings"></fx-icon>
                <fx-icon id="delete" url="carbon:close" class="but ml4 mr4" size="20" an="btn" br="square" scale="1.2" title="delete" @click=${this.btnClick} ?hidden=${this.hideDelete} fill="red"></fx-icon>
                <fx-icon id="edit" url="carbon:edit" class="but ml4 mr4" size="20" an="btn" br="square" scale="1" title="edit" @click=${this.btnClick} ?hidden=${this.hideEdit} fill="green" back=${this.editMode ? 'yellow' : ''}></fx-icon>
                <fx-icon for="load" url="carbon:add" class="but ml4 mr4" size="20" an="btn" br="square" scale="1.2" @click=${() => this.$qs('#load').click()} title="load file" ?hidden=${this.hideAdd} fill="blue"></fx-icon>
                <input id="load" type="file" style="display: none" accept=${this.type === 'doc' ? '.docx,.doc' : '*'} @change=${this.btnClick}/>
            </div>
        `
    }

    get get_txt() {
        if (this.type !== 'txt') return null;
        let cell = this.cell;
        this.min = cell?.minH || 20;
        return html`<textarea class="${this.cell?.class || ''}" id="cell-text-area" style="border: none; outline: none; resize: none; width: 100%; height: 100%; ${this.cell?.style || ''}" @input=${e => { cell.source = e.target.value; this.$update(); }}>${cell?.source || ''}</textarea>`
    }
    get get_diff() {
        if (this.type !== 'diff') return null;
        this.resize_fire_name = 'resize-splitter-diff';
        return html`
            <fx-jdiff class="h100" .src=${this.cell?.source || ''}></fx-jdiff>
        `
    }
    get get_iframe() {
        if (this.type !== 'iframe') return null;
        this.async(() => this.ctrl = this.$qs('fx-jhtml'), 20);
        return html`
            <fx-jhtml .cell=${this.cell} ?editMode=${this.editMode} style="width: 100%; height: 100%; border: none; overflow: hide; min-height: 0px;" .base=${this.base}></fx-jhtml>
        `
    }
    get get_tmp() {
        if (this.type !== 'tmp') return null;
        return html`
            <fx-jtmp .jupyter=${this} .src=${this.cell?.source} style="width: 100%; height: 100%; border: none; overflow: auto; min-height: 0px;" .base=${this.base}></fx-jtmp>
        `
    }
    get get_table() {
        if (this.type !== 'table') return null;
        this.min = this.cell?.minH || 174;
        return html`
            <fx-table .data=${this.cell?.source?.data || []} .base=${this.base}></fx-table>
        `
    }
    get get_link() {
        if (this.type !== 'link') return null;
        this.min = this.cell?.minH || 32;
        this.async(() => this.ctrl = this.$qs('fx-jlink'), 20);
        return html`
            <fx-jlink .cell=${this.cell} .base=${this.base} .url=${this.cell?.url} .base=${this.base}></fx-jlink>
        `
    }
    get get_code() {
        if (this.type !== 'code') return null;
        this.min = this.cell?.minH || 16;
        return html`
            <fx-jcode .cell=${this.cell} .base=${this.base} .base=${this.base}></fx-jcode>
        `
    }
    get get_exe() {
        if (this.type !== 'exe') return null;
        this.min = this.cell?.minH || 32;
        return html`
            <fx-jexe .cell=${this.cell} .base=${this.base} .base=${this.base}></fx-jexe>
        `;
    }
    get get_cherry() {
        if (this.type !== 'cherry') return null;
        this.min = this.cell?.minH || 174;
        this.async(() => this.ctrl = this.$qs('fx-jcherry'), 20);
        return html`
            <fx-jcherry .cell=${this.cell} .base=${this.base} .editMode=${this.editMode}  style="width: 100%; height: 100%; border: none; overflow: ${this.editMode ? 'hidden' : 'auto'}; min-height: 0px;"></fx-jcherry>
        `
    }
    get get_md() {
        if (this.type !== 'md') return null;
        this.min = this.cell?.minH || 174;
        return html`
            <fx-jmd class="overflow-y" .src=${this.cell?.source} .base=${this.base}></fx-jmd>
        `
    }
    get get_gallery() {
        if (this.type !== 'gallery') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jgallery class="vertical w100 h100 relative flex" .cell=${this.cell} .base=${this.base}></fx-jgallery>`
    }
    get get_todo() {
        if (this.type !== 'todo') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jtodo class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .base=${this.base}></fx-jtodo>`
    }
    get get_sheet() {
        if (this.type !== 'sheet') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jsheet class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .base=${this.base}></fx-jsheet>`
    }
    get get_spreadsheet() {
        if (this.type !== 'spreadsheet') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jspreadsheet class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .base=${this.base}></fx-jspreadsheet>`
    }
    get get_svg() {
        if (this.type !== 'svg') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jsvg class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .editMode=${this.editMode} .base=${this.base}></fx-jsvg>`
    }
    get get_excalidraw() {
        if (this.type !== 'excalidraw') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jexcalidraw class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .base=${this.base}></fx-jexcalidraw>`
    }
    get get_money() {
        if (this.type !== 'money') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jmoney class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .base=${this.base}></fx-jmoney>`
    }
    get get_family() {
        if (this.type !== 'family') return null;
        this.min = this.cell?.minH || 20;
        return html`<fx-jfamily class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .base=${this.base}></fx-jfamily>`
    }
    get get_doc() {
        if (this.type !== 'doc') return null;
        this.min = this.cell?.minH || 400;
        this.async(() => this.ctrl = this.$qs('fx-jsdoc'), 20);
        return html`<fx-jsdoc ?editMode=${this.editMode} class="vertical w100 h100 relative flex overflow" .cell=${this.cell} .base=${this.base}></fx-jsdoc>`
    }

    toggleToolbar() {
        this.toolbarVisible = !this.toolbarVisible;
        this.$update();
    }
    render() {
        const h = this.notebook?.cells?.length === 1 ? `calc(100% - ${this.hideTop ? 18 : this.jupyter.allwaysShowToolbar || this.toolbarVisible ? 48 : 18}px)` : (this.cell?.cell_h || this.cell?.h || 100) + 'px';
        const cls = this.jupyter.allwaysShowToolbar ? 'mb4' : '';
        let size = this.cell?.size || this.cell.source?.length ? ((+this.cell.size || this.cell.source?.length || 0) / 1024 / 1024).toFixed(3) + ' mb' : '';
        return html`
            ${this.jupyter.showCellType ? html`
                <div class="horizontal pl2 pr2 absolute" style="pointer-events: none; color: red; z-index: 99; top: -10px; transition: top 0.3s ease;">
                    <span style="font-size: 12px;">${this.cell?.cell_type}</span>
                    <span class="ml8" style="font-size: 12px;" ?hidden=${!size}>${size}</span>
                </div>
            ` : html``}
            <div class="toolbar-container ${cls}" style="transition: max-height 0.3s ease, opacity 0.3s ease; max-height: ${this.jupyter.allwaysShowToolbar || this.toolbarVisible ? '36px' : '0px'}; opacity: ${this.jupyter.allwaysShowToolbar || this.toolbarVisible ? '1' : '0'}; overflow: hidden;" ?hidden=${this.hideTop}>
                ${this.getTop}
            </div>
            <div class="toolbar-toggle absolute" style="height: 4px; cursor: pointer; width: 100%; position: relative; display: flex; justify-content: end; align-items: center; z-index: 10; top: 2px;" ?hidden=${this.hideTop || this.jupyter.allwaysShowToolbar} @click=${this.toggleToolbar}>
                <div class="btn-tool"></div>
            </div>
            <div class="${this.selected ? 'brs br' : ''} ${!this.cell.hideBorder && this.showBorder ? 'bral' : ''} cell vertical w100 relative overflow-h box flex  ${this.cell?.class || ''}" style="height: ${h}; min-height: ${this.min || 60}px; ${this.cell?.style || ''}">
                ${this['get_' + this.type]}
            </div>
            <fx-splitter size=0 min=${this.cell.min || this.min || 60} bottom @resize=${this.splitterResize} resize_fire_name=${this.resize_fire_name || 'resize'}></fx-splitter>
        `
    }
}
customElements.define('fx-jupyter-cell', FxJupyterCell);
