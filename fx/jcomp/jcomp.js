import { FxElement, html, css } from '/fx.js';

import '../jtmp/jtmp.js';

export class FxJComp extends FxElement {
    static properties = {
        base: { type: Object },
        cell: { type: Object },
        url: { type: String, default: '', notify: true },
        checkedURL: { type: String, default: '' },
        editMode: { type: Boolean, notify: true },
        useIframe: { type: Boolean },
        type: { type: String, default: 'text/html' }
    }
    get isAttachments() { return this.cell?.attachment || this.cell?.attachments }

    constructor() {
        super();
        this.handleResize = this.onResize.bind(this);
        this.handleChange = this.onChange.bind(this);
    }
    connectedCallback() {
        super.connectedCallback();
        window.addEventListener('resize', this.handleResize);
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        window.removeEventListener('resize', this.handleResize);
    }

    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            this.isReady = true;
            this.init();
        })
    }

    'url-changed'(e) {
        this.checkedURL = this.url = FX.checkUrl(e);
        this._onUrlChanged();
    }
    _onUrlChanged() {
        this.setIframe();
    }

    'editMode-changed'(e) {
        this._onEditModeChanged();
    }
    _onEditModeChanged() { 
        this.setIframe();
    }

    onChange(e) {
        this.saveAttachment(e.detail);
        this._onChange();
    }
    _onChange(e) {

     }

    onResize() {
        this._onResize();
    }
    _onResize() {
        this.setIframe();
     }

    async init() {
        if (this.isAttachments && this.base.dbLocal) {
            let db = this.db = this.base.dbLocal,
                _id = this.$root?.notebook?._id || this.base.idInfo,
                blob;
            blob = this.$root?.notebook?._attachments?.[ this.cell.ulid]?.data;
            if (!blob)
                try { blob = await db.getAttachment(_id, this.cell.ulid) } catch (error) { }
            if (!blob) {
                _id = this._idFile = this.cell?._idFile || 'file:' + this.base.fxSelected._id + '-' + this.cell.ulid;
                try { blob = await db.getAttachment(_id, 'file') } catch (error) { }
            }
            if (blob)
                this.checkedURL = URL.createObjectURL(blob);
        } else {
            let url = this.cell?.url || this.cell?.source || this.url;
            this.checkedURL = this.url = FX.checkUrl(url);
            this.$update();
        }
        this.setIframe();
        this.$update();
    }
    loadFile(file) {
        if (file) {
            this.async(() => {
                this.checkedURL = URL.createObjectURL(file);
                this.saveAttachment(file);
                this._onLoadFile();
            })
        }
    }
    _onLoadFile() {
        this.setIframe();
     }
    saveAttachment(blob) {
        let notebook = this.$root?.notebook;
        // let notebook = this.base.notebook;
        // const activeTab = this.base.main.fxTabs.activeTabName;
        // if (activeTab && activeTab.startsWith('add-info')) {
        //     const tabIndex = parseInt(activeTab.split('-')[2]);
        //     notebook = this.base.addNotebooks?.[tabIndex];
        // }
        this.checkedURL = URL.createObjectURL(blob);
        if (this.cell && notebook) {
            this.cell.size = blob.size;
            this.cell.attachment = blob.name || this.cell.ulid;
            if (blob.name)
                this.cell.url = blob.name;
            notebook._attachments ||= {};
            notebook._attachments[this.cell.ulid] = {
                content_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                data: blob
            }
        }
        this.$update();
    }
    async deleteAttachment(deleteAttachment = false) {
        if (this.cell) {
            this.cell.url = this.cell.size = this.cell.source = this.cell.ext = '';
            this.cell.attachment = false;
            if (deleteAttachment) {
                this.base.toDeleteAttachments ||= [];
                this.base.toDeleteAttachments.push({ _id: this.base, ulid: this.cell.ulid, _rev: null });
                delete this.base.notebook._attachments?.[this.cell.ulid];
            }
            delete this.cell.url;
            delete this.cell.ext;
            delete this.cell.source;
            delete this.cell._idFile;
            delete this.cell.size;
            delete this.cell.attachment;
        }
        this.src = this.ext = this.checkedURL = this.url = '';
        this.setIframe();
    }

    setIframe(isReady = this.isReady, useIframe = this.useIframe) {
        if (!isReady || !useIframe) return;
        const iframe = this.iframe = this.$qs('iFrame');;
        if (!iframe) return;
        try {
            iframe.addEventListener('load', () => {
                try {
                    if (iframe.contentDocument)
                        iframe.contentDocument.addEventListener("change", this.handleChange);
                } catch (error) { }
            })
            const src = this.srcdoc(this.checkedURL || '');
            iframe.src = URL.createObjectURL(new Blob([src], { type: this.type }));
            this.$update();   
        } catch (error) { }
    }

    render() {
        return html`
            <iframe style="border: none; width: 100%; height: 100%;"></iframe>
        `
    }
    srcdoc(url) {
        return url;
    }
}
customElements.define('fx-jcomp', FxJComp);
