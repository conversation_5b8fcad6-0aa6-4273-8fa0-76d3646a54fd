import { FxElement, html, css } from '../../fx.js';
import '../avatar/avatar.js';

customElements.define('fx-jfamily', class FxJFamily extends FxElement {
    render() {
        return html`
            <iframe ref="editor" srcdoc=${this.srcdoc || ''} style="border: none; width: 100%; height: 100%"></iframe>
            <input id="loadPhoto" type="file" style="display: none" @change=${this.addAvatar}/>
            ${this.propsAvatar?.showAvatar ? html`<fx-avatar .props=${this.propsAvatar} @changeAvatar=${this.changeAvatar}></fx-avatar>` : html``}
        `
    }
    static properties = {
        base: { type: Object },
        cell: { type: Object },
        data: { type: Array },
        propsAvatar: { type: Object }
    }

    async firstUpdated() {
        super.firstUpdated();
        const response = await fetch('/fx/~/family-chart/create-tree/index.html')
        this.srcdoc = await response.text();
        setTimeout(() => {
            const doc = this.$qs('iframe').contentDocument;
            this.updateIframeData();
            doc.addEventListener('changed', e => {
                if (this.cell)
                    this.cell.data = this.data = e.detail;
            })
            doc.addEventListener('show-add-info', async e => {
                e.stopPropagation();
                const base = this.base || FX.base;
                let _id = 'info:' + base.fxSelected._id + ':cell-' + this.cell.ulid + ':family-' + e.detail.id;
                console.log(_id);
                await base.getInfo(_id, base.dbLocal, 'addNotebook');
                this.async(() => {
                    const tabIndex = base.addNotebooks.length - 1;
                    const tabId = `add-info-${tabIndex}`;
                    base.main.fxTabs.selectTab('', tabId, true);
                }, 100)
                this.$update();
            })
            doc.addEventListener('select-avatar', e => {
                const d = e.detail.d;
                this.propsAvatar = { d };
                this.$id('loadPhoto').click();
            })
        }, 500)
        this.$update();
    }
    addAvatar(e) {
        const file = e.target.files[0];
        e.target.value = null;
        if (!file) return;
        const reader = new FileReader();
        reader.addEventListener("load", async () => {
            this.propsAvatar ||= { width: 400, height: 600 };
            this.propsAvatar.src = reader.result;
            this.propsAvatar.showAvatar = true;
            this.$update();
            const img = await new Promise((resolve, reject) => {
                this.$listen('_changeAvatar', (e) => {
                    resolve(this.propsAvatar.img || e.detail);
                })
            })
            if (img) {
                this.propsAvatar.d.data.data.avatar = img;
                this.updateIframeData();
            }
            this.$update();
        }, false)
        reader.readAsDataURL(file);
    }

    updateIframeData() {
        const iframe = this.$qs('iframe');
        if (iframe && iframe.contentDocument && iframe.contentDocument.store) {
            const doc = iframe.contentDocument;
            if (this.cell?.data || this.data) {
                doc.store.update.data(this.cell?.data || this.data);
                doc.store.update.tree();
            }
        }
    }
})
