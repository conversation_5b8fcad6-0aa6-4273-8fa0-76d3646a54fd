import { FxElement, html, css } from '../../../fx.js';

import './events-weeks.js';
import '../splitter/splitter.js';

customElements.define('fx-events', class FxEvents extends FxElement {
    static properties = {
        base: { type: Object },
        item: { type: Object, notify: true },
        events: { type: Object },
        showRight: { type: Boolean, default: true },
        sortedEvents: { type: Array, default: [] },
        lastCopyEvent: { type: Object },
        widthR: { type: Number, default: 280, save: true }
    }
    get weeks() { return this.$qs('fx-events-weeks') }
    get filterEvents() { return (this.sortedEvents || []).filter(i => i && !i._deleted) }

    async firstUpdated() {
        super.firstUpdated();
        await this.sleep();
        this.init();
    }

    'item-changed'(e) {
        this.events = this.sortedEvents = undefined;
        this.weeks.events = this.events;
        this.weeks?.initCanvas();
        this.async(async () => {
            this.$update();
            this.hideWeeks = false;
            await this.init();
            this.clearCopyEvent();
            this.idx = undefined;
        })
    }

    async init() {
        let db = this.db = this.base.dbLocal,
            _id = this.base.fxSelected._id,
            _doc = { items: [] };
        _id = 'events:' + _id;
        this.events = await this.base.getBsDraft(_id, _doc);
        this.sortEvents();
    }

    pointerDown(idx) {
        this.idx = idx;
        this.$update();
    }
    sortEvents() {
        if (!this.events?.doc?.items) return;
        this.sortedEvents = FX.sortBy(this.events.doc.items, 'date1');
        this.weeks.events = this.events || [];
        this.weeks._start = this.sortedEvents[0]?.date1;
        this.weeks?.initCanvas();
        this.$update();
    }
    addEvent(isPeriod) {
        let _id = 'events:' + this.base.fxSelected._id;
        const color = `oklch(${Math.floor(Math.random() * 101)}% ${(Math.random() * 0.37).toFixed(2)} ${Math.floor(Math.random() * 361)})`;
        this.events ||= new this.base.BS_ITEM({ _id, type: 'events', isLoad: true, doc: { items: [], parentId: this.base.fxSelected._id } });
        this.events.doc.items ||= [];
        this.events.doc.parentId = this.base.fxSelected._id;
        let date1 = new Date().toISOString().split('T');
        date1 = date1[0] + 'T12:00';
        const item = { date1, isPeriod, color };
        if (isPeriod) item.date2 = date1;
        this.events.doc.items.push(item);
        this.sortEvents();
        setTimeout(() => {
            let idx = this.filterEvents.length - 1;
            //this.$qs('#event-' + idx).scrollIntoView({ behavior: "smooth", block: "end" });
        }, 100)
    }
    async deleteEvent() {
        let i = this.filterEvents[this.idx || 0],
            idx = this.events.doc.items.indexOf(i);
        this.events.doc.items.splice(idx, 1);
        this.sortEvents();
    }
    years(doc) {
        const d1 = (new Date(doc.date1)).getTime();
        const d2 = (new Date(doc.date2 || new Date())).getTime();
        const diff = Math.abs(d2 - d1);
        return (diff / 1000 / 60 / 60 / 24 / 365.25).toFixed(2);
    }
    async showAddInfo(e, event) {
        e.stopPropagation();
        const base = this.base || FX.base;
        if (!base || !base.fxSelected) return;
        if (!event.idInfo) {
            event.idInfo = 'info:' + base.fxSelected._id + ':event:' + FX.ulid();
            this.fire('changeInfo', this.id);
        }
        let _id = event.idInfo;
        await base.getInfo(_id, base.dbLocal, 'addNotebook');
        this.async(() => {
            const tabIndex = base.addNotebooks.length - 1;
            const tabId = `add-info-${tabIndex}`;
            base.main.fxTabs.selectTab('', tabId, true);
        }, 100)
        this.$update();
    }
    async copyEvent() {
        if (!this.lastCopyEvent) {
            this.base.tree.singleCheckMode = true;
            this.lastCopyEvent = this.filterEvents[this.idx || 0];
            this.fire('copyEvent', { event: this.lastCopyEvent });
            this.$update();
        } else {
            let res = await this.base.tree.allItems.map(async i => {
                if (i.checked) {
                    let _id = 'events:' + i._id;
                    let event = await this.base.getBsDraft(_id, { items: [] });
                    event.doc.items.push(this.lastCopyEvent);
                }
            })
            await Promise.all(res);
            this.clearCopyEvent();
            this.fire('pasteEvent', { event: this.lastCopyEvent });
            this.$update();
        }
    }
    clearCopyEvent() {
        this.base.tree.clearChecked();
        this.base.tree.singleCheckMode = false;
        this.lastCopyEvent = undefined;
        this.$update();
    }
    linkClick(url) {
        if (url) {
            window.open(url, '_blank').focus();
        }
    }
    async _colorClick(e, i) {
        const res = await FX.colorPicker(i.color, 'events-color-picker');
        if (res?.detail) {
            i.color = res.detail;
            this.$update();
        }
    }
    resizeSplitter(e) {
        FX.throttle('resize-splitter', () => {
            this.widthR = e.detail.width;
        }, 16)
    }

    static styles = css`
        :host {
            box-sizing: border-box;
        }
        input {
            border: none;
            border-bottom: 1px solid lightgray; 
            outline: none;
            min-width: 0px; 
            width: calc(100% - 1px); 
            /* color: gray;  */
            font-size: 15px;
            height: 20px;
            font-family: Arial;
            background-color: transparent!important;
            cursor: pointer;
        }
        .inpt::-webkit-input-placeholder {
            color: lightgray;
        }
        textarea {
            outline: none;
            -moz-appearance: none;
            resize: none;
            border: none;
            border-bottom: 1px solid lightgray;
            /* color: #333; */
            font-size: 15px;
            font-family: Arial;
            width: 100%;
            background-color: transparent;
            word-break: break-all;
        }
        textarea::-webkit-scrollbar {
            display: none;
        }
    `

    render() {
        return html`
            <div class="w100 h100 horizontal flex overflow-h box relative">
                <div class="flex overflow w100">
                    <fx-events-weeks .item=${this.item}></fx-events-weeks>
                </div>
                ${this.showRight ? html`
                    <fx-splitter left min="200" size="0" @handle-drag-splitter=${this.resizeSplitter}></fx-splitter>
                    <div class="vertical br m2 box" style="min-width: ${this.widthR}px; max-width: ${this.widthR}px;">
                        <div class="horizontal w100 brb box p5">
                            <fx-icon url="cb-close" br="square" fill="tomato" class="ml2" an="btn" @click=${this.deleteEvent} title="Удалить" ?hidden=${!(this.idx || this.idx === 0)}></fx-icon>
                            <fx-icon fill=${this.lastCopyEvent ? "var(--fx-color-selected)" : ""} class="ml8" url=${this.lastCopyEvent ? 'lucide:copy-plus' : 'lucide:copy'} br="square" an="btn" @click=${this.copyEvent} title="Копировать" ?hidden=${!(this.idx || this.idx === 0)}></fx-icon>
                            <fx-icon fill='tomato' class="ml8" url='lucide:copy-x' br="square" an="btn" @click=${this.clearCopyEvent} title="Очистить скопированное" ?hidden=${!this.lastCopyEvent}></fx-icon>
                            <div class="flex"></div>
                            <fx-icon url="material-symbols-light:splitscreen-add-outline-rounded" scale="1"  br="square" class="mr8" an="btn" @click=${e => this.addEvent(true)} title="Добавить период"></fx-icon>
                            <fx-icon url="material-symbols-light:variable-add-outline-rounded" scale=1 br="square" class="mr16" an="btn" @click=${e => this.addEvent()} title="Добавить событие"></fx-icon>
                            <fx-icon url="cb-rotate-360" br="square" class="mr2" an="btn" style="margin-left: auto;" @click=${this.sortEvents} title="Обновить"></fx-icon>
                        </div>
                        <div class="overflow">
                            ${this.filterEvents.map((i, idx) => html`
                                <div class="m8 box" id=${'event-' + idx} @pointerdown=${e => this.pointerDown(idx)} style="padding: 4px; outline: ${this.idx === idx ? 2 : 1}px solid ${this.idx === idx ? 'var(--fx-color-selected)' : 'lightgray'}; border-radius: 4px; margin-bottom: 4px; background-color: hsla(${i?.isPeriod ? 180 : 90}, 70%, 70%, .2); overflow: hidden;">
                                    <div class="horizontal flex">
                                        <textarea class="inpt" .value=${i.label || ''} @change=${e => { i.label = e.target.value }} placeholder="event"></textarea>
                                        <div class="vertical">
                                            <fx-icon  class="pointer" name="emojione-v1:circled-information-source" size="20" @click=${e => this.showAddInfo(e, i)} style="opacity: ${i.idInfo ? 1 : .2};" title="info"></fx-icon>
                                            <fx-icon class="pointer" name="fc-calendar" size="20" @click=${e => { i.showEvent = !i.showEvent; this.$update() }} title="показывать событие в календаре" style="opacity: ${i.showEvent ? 1 : .2}; display: ${i.isPeriod ? 'none' : ''};"></fx-icon>
                                        </div>
                                    </div>
                                    <input class="inpt" type="datetime-local" .value=${i.date1} @change=${e => { i.date1 = e.target.value }} placeholder="дата">
                                    ${i.isPeriod ? html`
                                        <input class="inpt" type="datetime-local" .value=${i.date2} @change=${e => { i.date2 = e.target.value }} placeholder="дата окончания">
                                    ` : html``}
                                    <div style="display: flex; align-items: center">
                                        <textarea class="inpt" .value=${i.group || ''} @change=${e => { i.group = e.target.value }} placeholder="group"></textarea>
                                        <div style="color: darkgray; font-size: 14px">${this.years(i)}</div>
                                    </div>
                                    <div style="opacity: .7; background: ${i.color}; height: 10px; border-radius: 0; margin-top: 8px;" @click=${e => this._colorClick(e, i)}></div>
                                    <div class="horizontal center">
                                        <input class="fxs" style="width: 60px; cursor: default" .value=${i.link1 || ''} @change=${e => { i.link1 = e.target.value }}>
                                        <fx-icon class="pointer" name="fxemoji:meridianglobe:22" @click=${e => this.linkClick(i.link1)} style="opacity: ${i.link1 ? 1 : .1}"></fx-icon>
                                        <input class="fxs" style="width: 60px; cursor: default" .value=${i.link2 || ''} @change=${e => { i.link2 = e.target.value }}>
                                        <fx-icon class="pointer" name="fxemoji:meridianglobe:22" @click=${e => this.linkClick(i.link2)} style="opacity: ${i.link2 ? 1 : .1}"></fx-icon>
                                        <input class="fxs" style="width: 60px; cursor: default" .value=${i.link3 || ''} @change=${e => { i.link3 = e.target.value }}>
                                        <fx-icon class="pointer" name="fxemoji:meridianglobe:22" @click=${e => this.linkClick(i.link3)} style="opacity: ${i.link3 ? 1 : .1}"></fx-icon>
                                    </div>
                                </div>
                            `)}
                        </div>
                    </div>
                ` : html``}
            </div>
        `
    }
})

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons =
{
    "cb-close": {
        "svg": "<path fill=\"currentColor\" d=\"M17.414 16L24 9.414L22.586 8L16 14.586L9.414 8L8 9.414L14.586 16L8 22.586L9.414 24L16 17.414L22.586 24L24 22.586z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "cb-copy": {
        "svg": "<path fill=\"currentColor\" d=\"M28 10v18H10V10zm0-2H10a2 2 0 0 0-2 2v18a2 2 0 0 0 2 2h18a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2\"/><path fill=\"currentColor\" d=\"M4 18H2V4a2 2 0 0 1 2-2h14v2H4Z\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "cb-rotate-360": {
        "svg": "<path d=\"M25.95 7.65l.005-.004c-.092-.11-.197-.206-.293-.312c-.184-.205-.367-.41-.563-.603c-.139-.136-.286-.262-.43-.391c-.183-.165-.366-.329-.558-.482c-.16-.128-.325-.247-.49-.367c-.192-.14-.385-.277-.585-.406a13.513 13.513 0 0 0-.533-.324q-.308-.179-.625-.341c-.184-.094-.37-.185-.56-.27c-.222-.1-.449-.191-.678-.28c-.19-.072-.378-.145-.571-.208c-.246-.082-.498-.15-.75-.217c-.186-.049-.368-.102-.556-.143c-.29-.063-.587-.107-.883-.15c-.16-.023-.315-.056-.476-.073A12.933 12.933 0 0 0 6 7.703V4H4v8h8v-2H6.811A10.961 10.961 0 0 1 16 5a11.111 11.111 0 0 1 1.189.067c.136.015.268.042.403.061c.25.037.501.075.746.128c.16.035.315.08.472.121c.213.057.425.114.633.183c.164.054.325.116.486.178c.193.074.384.15.57.235c.162.072.32.15.477.23q.268.136.526.286c.153.09.305.18.453.276c.168.11.33.224.492.342c.14.102.282.203.417.312c.162.13.316.268.47.406c.123.11.248.217.365.332c.167.164.323.338.479.512A10.993 10.993 0 1 1 5 16H3a13 13 0 1 0 22.95-8.35z\" fill=\"currentColor\"/>",
        "vb": "0 0 32 32",
        "strw": "0"
    },
    "fc-info": {
        "svg": "<circle cx=\"24\" cy=\"24\" r=\"21\" fill=\"#2196F3\"/><path fill=\"#fff\" d=\"M22 22h4v11h-4z\"/><circle cx=\"24\" cy=\"16.5\" r=\"2.5\" fill=\"#fff\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fc-calendar": {
        "svg": "<path fill=\"#CFD8DC\" d=\"M5 38V14h38v24c0 2.2-1.8 4-4 4H9c-2.2 0-4-1.8-4-4\"/><path fill=\"#F44336\" d=\"M43 10v6H5v-6c0-2.2 1.8-4 4-4h30c2.2 0 4 1.8 4 4\"/><g fill=\"#B71C1C\"><circle cx=\"33\" cy=\"10\" r=\"3\"/><circle cx=\"15\" cy=\"10\" r=\"3\"/></g><path fill=\"#B0BEC5\" d=\"M33 3c-1.1 0-2 .9-2 2v5c0 1.1.9 2 2 2s2-.9 2-2V5c0-1.1-.9-2-2-2M15 3c-1.1 0-2 .9-2 2v5c0 1.1.9 2 2 2s2-.9 2-2V5c0-1.1-.9-2-2-2\"/><path fill=\"#90A4AE\" d=\"M13 20h4v4h-4zm6 0h4v4h-4zm6 0h4v4h-4zm6 0h4v4h-4zm-18 6h4v4h-4zm6 0h4v4h-4zm6 0h4v4h-4zm6 0h4v4h-4zm-18 6h4v4h-4zm6 0h4v4h-4zm6 0h4v4h-4zm6 0h4v4h-4z\"/>",
        "vb": "0 0 48 48",
        "strw": "0"
    },
    "fxemoji:meridianglobe": {
        "svg": "<path fill=\"#0096D1\" d=\"M425.393 86.607C380.146 41.361 319.988 16.442 256 16.442S131.854 41.361 86.607 86.607S16.442 192.012 16.442 256S41.36 380.146 86.607 425.393S192.012 495.558 256 495.558s124.146-24.918 169.393-70.165S495.558 319.988 495.558 256S470.64 131.854 425.393 86.607M386.027 242.5c-1.141-38.785-7.187-75.873-17.566-108.605c16.922-4.791 32.653-10.738 47.349-17.882c30.041 34.253 49.265 78.207 52.307 126.487zM242.5 466.638c-20.989-5.949-40.869-25.655-57.048-56.984a228 228 0 0 1-5.844-12.219c11.593-2.202 23.68-3.935 36.277-5.158a428 428 0 0 1 26.615-1.739zm27-76.15c21.326.656 42.336 2.977 62.887 6.956a228 228 0 0 1-5.839 12.209c-16.179 31.329-36.059 51.036-57.048 56.984zm-27-26.963c-9.7.314-19.444.927-29.225 1.877c-15.111 1.467-29.588 3.622-43.422 6.429c-9.922-30.536-15.727-65.521-16.87-102.331H242.5zM152.984 242.5c1.143-36.816 6.95-71.805 16.874-102.345c23.712 4.87 47.989 7.663 72.642 8.375v93.97zM242.5 121.523c-21.327-.657-42.338-2.984-62.891-6.959a229 229 0 0 1 5.843-12.218c16.179-31.33 36.058-51.037 57.048-56.985zm27-76.161c20.989 5.948 40.869 25.655 57.048 56.985a228 228 0 0 1 5.871 12.282c-10.417 1.958-21.302 3.531-32.689 4.73a430 430 0 0 1-30.229 2.096zm81.038 44.597c-6.618-12.816-13.906-24.061-21.732-33.669c24.658 9.017 47.19 22.48 66.629 39.411c-11.359 4.975-23.438 9.21-36.287 12.755c-2.686-6.4-5.554-12.579-8.61-18.497m-189.076 0c-3.041 5.888-5.896 12.035-8.57 18.401c-13.017-3.574-25.073-7.775-36.326-12.659c19.438-16.93 41.97-30.393 66.628-39.41c-7.826 9.607-15.114 20.852-21.732 33.668m-17.892 43.84c-10.398 32.755-16.455 69.878-17.597 108.701h-82.09c3.041-48.266 22.254-92.208 52.281-126.457c14.553 7.039 30.243 12.923 47.406 17.756M125.973 269.5c1.142 38.814 7.196 75.928 17.589 108.678c-16.978 4.812-32.778 10.77-47.359 17.823c-30.049-34.255-49.278-78.215-52.32-126.501zm26.909 134.116a258 258 0 0 0 8.58 18.425c6.618 12.816 13.906 24.061 21.731 33.669c-24.647-9.014-47.171-22.469-66.604-39.389c11.31-4.92 23.428-9.151 36.293-12.705m206.215.051c12.792 3.547 24.916 7.797 36.26 12.702c-19.421 16.898-41.926 30.336-66.55 39.341c7.825-9.608 15.113-20.853 21.732-33.669c3.036-5.88 5.887-12.018 8.558-18.374m-16.954-31.825c-23.709-4.874-47.99-7.655-72.643-8.367V269.5h89.516c-1.144 36.815-6.95 71.803-16.873 102.342M269.5 242.5v-94.023a457 457 0 0 0 33.056-2.267c13.854-1.458 27.024-3.464 39.606-5.993c9.912 30.525 15.712 65.492 16.855 102.283zm146.193 153.618l.068-.141c-14.598-7.008-30.463-12.952-47.339-17.75c10.403-32.762 16.463-69.894 17.605-108.728h82.089c-3.045 48.343-22.315 92.347-52.423 126.619\"/>",
        "vb": "0 0 512 512",
        "strw": "0"
    },
    "material-symbols-light:splitscreen-add-outline-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M5 19v-4.615v.042v-.042zm.616 1q-.691 0-1.153-.462T4 18.384V15q0-.69.463-1.153t1.153-.462h12.769q.517 0 .903.28q.385.28.539.72H5.616q-.27 0-.443.173T5 15v3.385q0 .269.173.442t.443.173h8.269q.213 0 .356.143t.143.357t-.143.357t-.357.143zm0-9.365q-.691 0-1.153-.463T4 9.019V5.616q0-.691.463-1.153T5.616 4h12.769q.69 0 1.153.463T20 5.616v3.403q0 .69-.462 1.153t-1.153.463zm0-1h12.769q.269 0 .442-.174Q19 9.289 19 9.02V5.616q0-.27-.173-.443T18.385 5H5.615q-.269 0-.442.173T5 5.616v3.403q0 .27.173.443t.443.173m-.616 0V5zM19 20h-1.73q-.214 0-.358-.143t-.143-.357t.144-.357t.356-.143H19v-1.73q0-.214.143-.358t.357-.143t.357.144t.143.356V19h1.73q.214 0 .358.143t.143.357t-.144.357t-.356.143H20v1.73q0 .214-.143.358t-.357.143t-.357-.144t-.143-.356z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "material-symbols-light:variable-add-outline-rounded": {
        "svg": "<path fill=\"currentColor\" d=\"M4.808 16q-.343 0-.576-.23T4 15.2V8.8q0-.34.232-.57T4.808 8h14.384q.344 0 .576.232t.232.576V9.5q0 .213-.144.356t-.357.144t-.356-.144T19 9.5V9H5v6h8.5q.213 0 .356.144t.144.357t-.144.356T13.5 16zM5 15V9zm14 1h-2.5q-.213 0-.356-.144T16 15.499t.144-.356T16.5 15H19v-2.5q0-.213.144-.356t.357-.144t.356.144t.143.356V15h2.5q.213 0 .356.144t.144.357t-.144.356T22.5 16H20v2.5q0 .213-.144.356t-.357.144t-.356-.144T19 18.5z\"/>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "lucide:copy": {
        "svg": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><rect width=\"14\" height=\"14\" x=\"8\" y=\"8\" rx=\"2\" ry=\"2\"/><path d=\"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\"/></g>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "lucide:copy-x": {
        "svg": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"m12 12l6 6m-6 0l6-6\"/><rect width=\"14\" height=\"14\" x=\"8\" y=\"8\" rx=\"2\" ry=\"2\"/><path d=\"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\"/></g>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "emojione-v1:circled-information-source": {
        "svg": "<g fill=\"#1b75bb\"><path d=\"M31.891 0C14.307 0 0 14.307 0 31.891s14.307 31.891 31.891 31.891s31.891-14.307 31.891-31.891S49.475 0 31.891 0m0 55.877c-13.227 0-23.986-10.76-23.986-23.986S18.664 7.903 31.891 7.903s23.986 10.761 23.986 23.988s-10.76 23.986-23.986 23.986\"/><path d=\"M31.934 17.919c-1.101 0-2.05-.337-2.836-1.013q-1.178-1.017-1.178-2.881q-.002-1.688 1.206-2.777q1.208-1.094 2.808-1.094c1.027 0 1.942.328 2.736.99c.792.659 1.193 1.621 1.193 2.881c0 1.223-.39 2.174-1.166 2.868q-1.164 1.027-2.763 1.026m3.929 8.208v22.957c0 1.59-.382 2.793-1.137 3.608q-1.131 1.22-2.879 1.221c-1.748.001-2.113-.415-2.838-1.25q-1.092-1.252-1.09-3.579V26.362q-.002-2.359 1.09-3.551c.725-.797 1.674-1.189 2.838-1.189s2.126.392 2.879 1.189c.755.793 1.137 1.903 1.137 3.316\"/></g>",
        "vb": "0 0 64 64",
        "strw": "0"
    },
    "lucide:copy-plus": {
        "svg": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M15 12v6m-3-3h6\"/><rect width=\"14\" height=\"14\" x=\"8\" y=\"8\" rx=\"2\" ry=\"2\"/><path d=\"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\"/></g>",
        "vb": "0 0 24 24",
        "strw": "0"
    }
}
FX.setIcons(usedIcons);