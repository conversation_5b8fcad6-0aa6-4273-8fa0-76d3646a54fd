import { FxElement, html } from '/fx.js';
import { $styles } from './table-template.x.js';
import '../button/button.js';
import '../splitter/splitter.js';
import '../table/table.js';
import '../jcode/jcode.js';

export class FxTableTemplate extends FxElement {
    static properties = {
        base: { type: Object },
        data: { type: Object },
        templatesList: { type: Array, default: [] },
        selected: { type: String, default: '' },
        src: { type: String, default: '' },
        templateId: { type: String, default: '' }
    }

    async firstUpdated() {
        super.firstUpdated();
        this.base = FX.base || {};
        this.templatesList = await this.getTemplatesList();
        await this.getTemplateSrc();
        this.async(() => {
            if (this.templateId) {
                this.getTemplateSrc(this.templateId);
                this.selected = this.templateId;
            }
        })
    }

    async getTemplatesList() {
        if (!this.base?.dbLocal) return [];
        try {
            let result = await this.base.dbLocal.allDocs({
                include_docs: true,
                startkey: 'table-template:',
                endkey: 'table-template:\ufff0'
            })
            result = result.rows.map(row => row.id.replace('table-template:', ''));
            result.unshift('');
            return result;
        } catch (error) {
            console.error('Error getting templates list:', error);
            return [];
        }
    }
    parseTemplateArray(arr = []) {
        let data = arr.map(d => {
            const processed = { ...d };
            Object.keys(processed).forEach(key => {
                const value = processed[key];
                if (typeof value === 'string' && value.trim()) {
                    try {
                        if (value.includes('=>') || value.startsWith('function')) {
                            processed[key] = eval(`(${value})`);
                        }
                    } catch (error) {
                        console.warn(`Failed to parse function in ${key}:`, value, error);
                    }
                }
            })
            return processed;
        })
        return data;
    }
    parseTemplateObject(obj = {}) {
        let data = {};
        Object.keys(obj).map(k => {
            const processed = { ...obj[k] };
            Object.keys(processed).forEach(key => {
                const value = processed[key];
                if (typeof value === 'string' && value.trim()) {
                    try {
                        if (value.includes('=>') || value.startsWith('function')) {
                            processed[key] = eval(`(${value})`);
                        }
                    } catch (error) {
                        console.warn(`Failed to parse function in ${key}:`, value, error);
                    }
                }
            })
            data[k] = processed;
        })
        return data;
    }
    serializeWithFunctions(obj) {
        return JSON.stringify(obj, (key, value) => {
            if (typeof value === 'function') {
                return value.toString();
            }
            return value;
        }, 4)
    }
    async getTemplateSrc(name, src) {
        this.selected = name;
        this.$qs('input').value = name || '';
        if (!this.base?.dbLocal) return '';
        try {
            let jsSource, result;
            if (name)
                result = await this.base.dbLocal.get('table-template:' + name);
            const hasExistingSettings = result?.columns ||
                result?.footerCalculations ||
                result?.options;
            if (hasExistingSettings) {
                const templateData = {
                    columns: this.parseTemplateArray(JSON.parse(result.columns || '[]')),
                    footerCalculations: this.parseTemplateObject(JSON.parse(result.footerCalculations || '{}')),
                    options: JSON.parse(result.options || '{}')
                }
                this.data = templateData;
                jsSource = `// Table Template Configuration
// Текущие настройки таблицы

table.data = ${this.serializeWithFunctions(templateData)}

// Примеры использования:
//
// Типы колонок (typeColumn):
// - "input" - поле ввода
// - "textarea" - многострочное поле
// - "selector" - выпадающий список
// - "checkbox" - чекбокс
// - "html" - HTML контент
//
// Типы вычислений футера (footerCalculations):
// - "sum" - сумма
// - "count" - количество
// - "avg" - среднее значение
// - "custom" - пользовательская функция
//
// Функции вычисления (calc):
// - "(e) => e.field1 * e.field2" - умножение полей
// - "(e) => e.price * (1 + e.tax/100)" - с налогом
`
            } else {
                try {
                    if (src) {
                        jsSource = src;
                    } else {
                        const response = await fetch('/fx/table-template/example-template.js');
                        jsSource = await response.text();
                    }
                    this.parseSrc(jsSource);
                } catch (error) {
                    console.warn('Could not load example template, using default:', error);
                    jsSource = `table.data = { columns: [], footerCalculations: {}, options: {} }`;
                }
            }
            this.src = jsSource;
            this.$update()
        } catch (error) {
            console.error('Error getting template src:', error);
            return '';
        }
    }
    async parseSrc(editedJs) {
        if (editedJs) {
            try {
                let parsedData;
                let cleanJs = editedJs
                    .replace(/\/\*[\s\S]*?\*\//g, '')
                    .replace(/\/\/.*$/gm, '');
                const objectMatch = cleanJs.match(/({[\s\S]*})/);
                if (objectMatch) {
                    const objectStr = objectMatch[1];
                    try {
                        parsedData = JSON.parse(objectStr);
                    } catch (jsonError) {
                        const evalStr = `(${objectStr})`;
                        parsedData = eval(evalStr);
                    }
                } else {
                    tconsole.error('Error parsing JavaScript code');
                }
                if (parsedData && typeof parsedData === 'object') {
                    if (parsedData.columns) this.columns = this.parseTemplateArray(parsedData.columns);
                    if (parsedData.footerCalculations) this.footerCalculations = this.parseTemplateObject(parsedData.footerCalculations);
                    if (parsedData.options) this.options = parsedData.options;
                    if (parsedData.data) this.data ||= parsedData.data;
                    const templateData = {
                        columns: this.columns || [],
                        footerCalculations: this.footerCalculations || {},
                        options: this.options || {}
                    }
                    this.data = templateData;
                    this.$update();
                    // this.updateVisibleRows();
                } else {
                    tconsole.error('Error parsing JavaScript code');
                }
            } catch (parseError) {
                console.error('Error parsing JavaScript code:', parseError);
            }
        }
    }
    async saveTemplate(e, templateId) {
        templateId ||= this.$qs('input').value;
        if (!this.base?.dbLocal || !templateId) return false;
        try {
            const _id = 'table-template:' + templateId;
            const doc = {
                _id,
                type: 'table-template',
                // tableId: this.id,
                mode: this.mode,
                created: FX.dates().utc,
                columns: this.serializeWithFunctions(this.data.columns || []),
                footerCalculations: this.serializeWithFunctions(this.data.footerCalculations || {}),
                options: this.serializeWithFunctions(this.data.options || {})
            }
            const bs = new this.base.BS_ITEM({ _id, type: 'table-template', isNew: true, doc });
            console.log('Template saved:', templateId);
            this.templatesList.add(templateId);
            this.$update();
            this.base?.$update();
            return templateId;
        } catch (error) {
            console.error('Error saving template:', error);
            return false;
        }
    }
    async deleteTemplate(e, templateId) {
        templateId ||= this.$qs('input').value;
        if (!this.base?.dbLocal || !templateId) {
            return false;
        }
        try {
            const _id = 'table-template:' + templateId;
            const doc = await this.base.dbLocal.get(_id);
            const bs = new this.base.BS_ITEM({ _id, type: 'table-template', isNew: true, doc });
            bs._deleted = true;
            console.log('Template deleted:', templateId);
            this.templatesList.remove(templateId);
            this.$update();
            this.base?.$update();
            return true;
        } catch (error) {
            console.error('Error deleting template:', error);
            return false;
        }
    }

    onSrcChange() {
        this.async(() => {
            this.parseSrc(this.$qs('fx-jcode').source || '');
        }, 10)
    }

    static styles = [$styles]

    render() {
        return html`
            <div class="horizontal flex w100 h100 relative box overflow-h bral">
                <div class="vertical w100 h100 relative overflow-h" style="min-width: 160px; width: 160px; height: calc(100vh - 80px)">
                    <div class="vertical w100 h100 relative overflow-y">
                        ${this.templatesList.map(name => html`
                            <label class="w100 h28 brbl p4 pointer" ?selected=${this.selected === name} @click=${() => this.getTemplateSrc(name)}>${name || 'demo template'}</label>
                        `)}
                        <div class="flex"></div>
                    </div>
                    <input class="inp fm w100 h28 brtl brbl p4 box" placeholder="new template name">
                    <div class="vertical w100 no-flex p4">
                        <fx-icon class="mb10 mt2" url="carbon:save" scale=1 an="btn" br="square" @click=${this.saveTemplate} txt="Save new"></fx-icon>
                        <fx-icon url="carbon:trash-can" scale=1 an="btn" br="square" @click=${this.deleteTemplate} txt="Delete selected"></fx-icon>
                    </div>
                </div>
                <fx-splitter color="gray" vertical size="2"></fx-splitter>
                <div class="vertical w100 relative box overflow-h">
                    <fx-table class="w100" hide="cb" style="height: 180px" .data=${[{}, {}]} .columns=${this.data?.columns} .footerCalculations=${this.data?.footerCalculations} .options=${this.data?.options}></fx-table>
                    <fx-jcode class="flex w100 h100" .src=${this.src} @change=${this.onSrcChange}></fx-jcode>
                </div>
            </div>
        `
    }
}

customElements.define('fx-table-template', FxTableTemplate);
